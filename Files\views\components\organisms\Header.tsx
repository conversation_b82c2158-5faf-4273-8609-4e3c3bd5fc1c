declare let React: any
const {useState} = React
const oreoLogo = require('../../../assets/images/oreo-logo.png')
import Menu from './Menu'
import Card from '../atoms/Card'
import MusicToggle from '../atoms/MusicToggle'
import {Language, translate} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface IHeaderProps {
  handleBack?: () => void
  goToPage?: (page: string) => void;
  gameStatus: any;
    className?: string;
    signOut: () => void;
}

const Header = (props: IHeaderProps) => {
  const {country} = useParams()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const refreshPage = () => {
    window.location.reload()
  }

  return (
    <div
     className={`flex mt-4 justify-center items-center relative w-full ${props.className}`}
    >
      {props.handleBack && (
        <button className='absolute left-4' onClick={props.handleBack}>
          <svg
            width='16'
            height='16'
            viewBox='0 0 16 16'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M14.9732 6.95109H3.50389L8.51465 1.80561C8.9151 1.39439 8.9151 0.719571 8.51465 0.308354C8.41966 0.210607 8.30683 0.133058 8.18261 0.0801464C8.0584 0.0272349 7.92524 0 7.79076 0C7.65628 0 7.52313 0.0272349 7.39891 0.0801464C7.2747 0.133058 7.16186 0.210607 7.06687 0.308354L0.300281 7.25687C0.205093 7.35442 0.129575 7.47028 0.0780485 7.59784C0.0265224 7.72539 0 7.86213 0 8.00022C0 8.13832 0.0265224 8.27505 0.0780485 8.40261C0.129575 8.53016 0.205093 8.64603 0.300281 8.74358L7.06687 15.6921C7.16193 15.7897 7.27479 15.8671 7.39899 15.92C7.5232 15.9728 7.65632 16 7.79076 16C7.9252 16 8.05832 15.9728 8.18253 15.92C8.30674 15.8671 8.41959 15.7897 8.51465 15.6921C8.60972 15.5945 8.68512 15.4786 8.73657 15.351C8.78802 15.2235 8.8145 15.0868 8.8145 14.9487C8.8145 14.8107 8.78802 14.674 8.73657 14.5464C8.68512 14.4189 8.60972 14.303 8.51465 14.2054L3.50389 9.0599H14.9732C15.5379 9.0599 16 8.58542 16 8.0055C16 7.42557 15.5379 6.95109 14.9732 6.95109Z'
              fill='white'
            />
          </svg>
        </button>
      )}
      {['aiming', 'congratulation', 'level-up'].includes(props.gameStatus) && (
        <button
          onClick={() => props.goToPage('leaderboard')}
          className='absolute left-4'
        >
          <Card
            className='!w-[80px] !p-1'
            content={(
              <div className='flex flex-col items-center'>
                <svg
                  width='20'
                  height='18'
                  viewBox='0 0 20 18'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M0 18V6H5.5V18H0ZM7.25 18V0H12.75V18H7.25ZM14.5 18V8H20V18H14.5Z'
                    fill='white'
                  />
                </svg>
                <p className='text-[8px]'>{translate('leaderboard', country as Language)}</p>
              </div>
            )}
          />
        </button>
      )}

      <img
        src={oreoLogo}
        className='w-[133px] cursor-pointer'
        onClick={refreshPage}
        alt='Oreo Logo'
      />
      <div className='absolute right-4 flex items-center gap-8'>
        <MusicToggle />
        <svg
        onClick={toggleMenu}
        width='23'
        height='16'
        viewBox='0 0 23 16'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
          <path
          d='M7.02778 16H23V13.3333H7.02778V16ZM0 9.33333H23V6.66667H0V9.33333ZM0 0V2.66667H23V0H0Z'
          fill='white'
        />
        </svg>
      </div>
      <Menu
        isOpen={isMenuOpen}
        onClose={toggleMenu}
        goToPage={props.goToPage || (() => {})}
        signOut={props.signOut}
      />
    </div>
  )
}

export default Header
