declare let React: any

import {UserData} from '../../types/UserData'
import {MAX_LEVEL} from '../../home'

const oreoRocket = require('../../../assets/images/embossment/rocket_unlock.png')
const oreoTelescope = require('../../../assets/images/embossment/telescope_unlock.png')
const oreoAstronaut = require('../../../assets/images/embossment/astronaut_unlock.png')
const oreoStarburst = require('../../../assets/images/embossment/starburst_unlock.png')
const oreoShootStar = require('../../../assets/images/embossment/shootstar_gold.png')

const shootingStarOreo = require('../../../assets/images/oreo-variant/shootingStar.png')
const rocketOreo = require('../../../assets/images/oreo-variant/rocket.png')
const astronautOreo = require('../../../assets/images/oreo-variant/astronaut.png')
const starOreo = require('../../../assets/images/oreo-variant/star.png')
const telescopeOreo = require('../../../assets/images/oreo-variant/telescope.png')
import { Language } from '../../config/translation';
import { translate } from '../../config/translation';
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface CatchOreoProps {
  user: UserData;
  displayOn: 'result' | 'game';
}

const oreoListNormal = [
  rocketOreo,
  telescopeOreo,
  astronautOreo,
  starOreo,
  shootingStarOreo,
]
const oreoListEmbossment = [
  oreoRocket,
  oreoTelescope,
  oreoAstronaut,
  oreoStarburst,
  oreoShootStar,
]
const CatchOreo = ({user, displayOn}: CatchOreoProps) => {
  const { country } = useParams();
  if (displayOn === 'game') {
    return (
      <div className='flex flex-col gap-2 items-center justify-center'>
        <div className='flex flex-wrap max-w-[100px] items-center justify-center gap-2'>
          {user.oreo_cookie.map((oreoIndex, index) => {
            if (!oreoListEmbossment[oreoIndex]) {
              console.log('🚀 ~Not loaded embossment oreoIndex:', oreoIndex)
              return null
            }
            if (oreoIndex === 4) {
              return (
                <img
                key={index}
                className={`${
                  user.is_success_bonus ? 'opacity-100' : 'opacity-30'
                } w-[28px] h-[28px] shrink-0`}
                src={oreoListEmbossment[oreoIndex]}
                alt=''
              />
              )
            }
            return (
              <img
              key={index}
              className={`${
                index < (user?.level ?? 0) ? 'opacity-100' : 'opacity-30'
              } w-[28px] h-[28px] shrink-0`}
              src={oreoListEmbossment[oreoIndex]}
              alt=''
            />
            )
          })}
        </div>
         <p className="text-[10px]">
          {translate('dunkAll5', country as Language)}
        </p>
      </div>
    )
  }

  if (displayOn === 'result') {
    return (
      <div className='flex gap-2'>
        {Array.from({length: user?.level ?? 0}).map((_, index) => (
          <div
              key={index}
              className='flex items-center justify-center rounded-full border-2 border-white w-[44px] h-[44px]'
            >
            <img
                className='w-[30px] h-[30px]'
                src={oreoListNormal[user.oreo_cookie[index]]}
                alt=''
              />
          </div>
        ))}
        {Array.from({
          length: MAX_LEVEL - (user?.level ?? 0),
        }).map((_, index) => (
          <div
            key={index}
            className='flex items-center justify-center rounded-full border-2 border-white w-[44px] h-[44px] bg-[#00227A]'
          >
            <svg
              width='14'
              height='19'
              viewBox='0 0 14 19'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M7 14.2143C7.46413 14.2143 7.90925 14.0337 8.23744 13.7122C8.56563 13.3907 8.75 12.9547 8.75 12.5C8.75 12.0453 8.56563 11.6093 8.23744 11.2878C7.90925 10.9663 7.46413 10.7857 7 10.7857C6.53587 10.7857 6.09075 10.9663 5.76256 11.2878C5.43437 11.6093 5.25 12.0453 5.25 12.5C5.25 12.9547 5.43437 13.3907 5.76256 13.7122C6.09075 14.0337 6.53587 14.2143 7 14.2143ZM12.25 6.5C12.7141 6.5 13.1592 6.68061 13.4874 7.0021C13.8156 7.32359 14 7.75963 14 8.21429V16.7857C14 17.2404 13.8156 17.6764 13.4874 17.9979C13.1592 18.3194 12.7141 18.5 12.25 18.5H1.75C1.28587 18.5 0.840752 18.3194 0.512563 17.9979C0.184374 17.6764 0 17.2404 0 16.7857V8.21429C0 7.75963 0.184374 7.32359 0.512563 7.0021C0.840752 6.68061 1.28587 6.5 1.75 6.5H2.625V4.78571C2.625 3.64907 3.08594 2.55898 3.90641 1.75526C4.72688 0.951529 5.83968 0.5 7 0.5C7.57453 0.5 8.14344 0.610853 8.67424 0.826231C9.20504 1.04161 9.68734 1.35729 10.0936 1.75526C10.4998 2.15322 10.8221 2.62568 11.042 3.14564C11.2618 3.66561 11.375 4.22291 11.375 4.78571V6.5H12.25ZM7 2.21429C6.30381 2.21429 5.63613 2.4852 5.14384 2.96744C4.65156 3.44968 4.375 4.10373 4.375 4.78571V6.5H9.625V4.78571C9.625 4.10373 9.34844 3.44968 8.85616 2.96744C8.36387 2.4852 7.69619 2.21429 7 2.21429Z'
                fill='white'
              />
            </svg>
          </div>
        ))}

        {user?.is_success_bonus ? (
          <div
            key={`${user.latest_usage_bonus_level}bonus`}
            className='flex items-center justify-center rounded-full border-2 border-white w-[44px] h-[44px]'
          >
            <img className='w-[30px] h-[30px]' src={shootingStarOreo} alt='' />
          </div>
        ) : (
          <div
            key={`${user.latest_usage_bonus_level}bonus`}
            className='flex items-center justify-center rounded-full border-2 border-white w-[44px] h-[44px] bg-[#00227A]'
          >
            <svg
              width='14'
              height='19'
              viewBox='0 0 14 19'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M7 14.2143C7.46413 14.2143 7.90925 14.0337 8.23744 13.7122C8.56563 13.3907 8.75 12.9547 8.75 12.5C8.75 12.0453 8.56563 11.6093 8.23744 11.2878C7.90925 10.9663 7.46413 10.7857 7 10.7857C6.53587 10.7857 6.09075 10.9663 5.76256 11.2878C5.43437 11.6093 5.25 12.0453 5.25 12.5C5.25 12.9547 5.43437 13.3907 5.76256 13.7122C6.09075 14.0337 6.53587 14.2143 7 14.2143ZM12.25 6.5C12.7141 6.5 13.1592 6.68061 13.4874 7.0021C13.8156 7.32359 14 7.75963 14 8.21429V16.7857C14 17.2404 13.8156 17.6764 13.4874 17.9979C13.1592 18.3194 12.7141 18.5 12.25 18.5H1.75C1.28587 18.5 0.840752 18.3194 0.512563 17.9979C0.184374 17.6764 0 17.2404 0 16.7857V8.21429C0 7.75963 0.184374 7.32359 0.512563 7.0021C0.840752 6.68061 1.28587 6.5 1.75 6.5H2.625V4.78571C2.625 3.64907 3.08594 2.55898 3.90641 1.75526C4.72688 0.951529 5.83968 0.5 7 0.5C7.57453 0.5 8.14344 0.610853 8.67424 0.826231C9.20504 1.04161 9.68734 1.35729 10.0936 1.75526C10.4998 2.15322 10.8221 2.62568 11.042 3.14564C11.2618 3.66561 11.375 4.22291 11.375 4.78571V6.5H12.25ZM7 2.21429C6.30381 2.21429 5.63613 2.4852 5.14384 2.96744C4.65156 3.44968 4.375 4.10373 4.375 4.78571V6.5H9.625V4.78571C9.625 4.10373 9.34844 3.44968 8.85616 2.96744C8.36387 2.4852 7.69619 2.21429 7 2.21429Z'
                fill='white'
              />
            </svg>
          </div>
        )}
      </div>
    )
  }
}

export default CatchOreo
