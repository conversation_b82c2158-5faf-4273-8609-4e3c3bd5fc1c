import {appBase} from '../lib/routes'
const base = appBase()
import '../index.css'
const oreoModel = require('../assets/3d/oreo-model-v5.glb')
const audioOnTarget = require('../assets/audio/on-target.mp3')

declare let React: any
declare let ReactRouterDOM: any
declare let THREE: any

const {useState, useEffect, useRef} = React
const {withRouter, useLocation, useParams, useHistory} = ReactRouterDOM

const backgroundTexture = require('../assets/space-bg.jpg')
import Joystick from './components/atoms/Joystick'

import Analytic from './components/atoms/Analytic'
import Header from './components/organisms/Header'
import Timer from './components/atoms/Timer'
import DunkNowBtn from './components/atoms/DunkNowBtn'
import Button from './components/atoms/Button'
import Title from './components/atoms/Title'
import Card from './components/atoms/Card'
import Footer from './components/organisms/Footer'
import Leaderboard from './components/pages/Leaderboard'
import ActionNeed from './components/pages/ActionNeed'
import UpdateLoginPin from './components/pages/UpdateLoginPin'
import Login from './components/pages/Login'
import {
  API_SUBMIT_FORM,
  API_REFRESH_TOKEN,
  API_GET_USER,
  API_SEND_DATA_TO_CMS,
} from './config/apiConfig'
import {UserData} from './types/UserData'
import ForgotPassword from './components/pages/ForgotPassword'
import PopupOreoStar from './components/molecules/PopupOreoStar'
import BonusLevel from './components/pages/BonusLevel'
import BuyProduct from './components/pages/BuyProduct'

import {getUtmParams, getCid} from './utils/tracking'
import ScanCookie from './components/pages/ScanCookie'
import {
  generateDunkNowId,
  generatePlayLevelId,
  generateSubmitPointId,
  generatePlayBonusId,
} from './utils/generateId'
import OreoSpaceDunk from './components/pages/OreoSpaceDunk'
import ThePrizes from './components/pages/ThePrizes'
import ScanButton from './components/atoms/ScanButton'
import CatchOreo from './components/atoms/CatchOreo'
import DropdownRegion from './components/atoms/DropdownRegion'
import Youtube from './components/atoms/Youtube'
import {shuffleArrayWithRules} from './utils/shuffleArray'
import Dekstop from './components/pages/Dekstop'
import HowToParticipate from './components/pages/HowToParticipate'
import {
  Language,
  translate,
  translateLevelUp,
  translateLevelUpPoint,
} from './config/translation'
import {translatePointsEarned} from './config/translation'

import {
  DunkNow,
  MissedPlayAgain,
  NewPlaySkip,
  PlayLevel,
  PlayBonus,
  SubmitPoint,
} from './analytics/events'

import getPrizeByRegion, {PrizeType} from './config/getPrizeByRegion'
import Landing from './components/pages/Landing'
import CookiePolicy from './components/pages/CookiePolicy'

export const MAX_LEVEL = 4

const OREO_SPEED = 0.1  // Reduced speed as requested

const DECELERATION = 0.5  // How quickly it slows down (0-1)

const OREO_DISTANCE_FROM_CAMERA_NORMAL = 3.5
const OREO_DISTANCE_FROM_CAMERA_IDLE = 5

const STAR_COUNT = 500
const STAR_SPREAD_X = 20
const STAR_SPREAD_Y = 20
const STAR_SPREAD_Z = 50
const MIN_STAR_SIZE = 0.2
const MAX_STAR_SIZE = 0.6

// Tambahkan konstanta untuk area ring
const RING_AREA = {
  width: 20,  // areaSizeX
  height: 30,  // areaSizeY
  depth: 10,  // maxDepth - minDepth
  minZ: -30,  // -maxDepth
}

// Tambahkan konstanta untuk mengatur kecepatan pergerakan bintang
const STAR_MOVE_SPEED = 0
const STAR_DIRECTION_CHANGE_PROBABILITY = 0  // Kemungkinan bintang berubah arah
let counter = 0
const Home = withRouter(() => {
  counter++
  console.log('rerender: ', counter)
  const {country} = useParams()

  const alltimePrize = getPrizeByRegion(country, 'alltime') as PrizeType

  // console.log({country})
  const soundOnTarget = useRef(
    new Audio(audioOnTarget)
  )

  const countryRef = useRef('')
  const history = useHistory()
  const containerRef = useRef(null)
  const renderer = useRef(null)
  const scene = useRef(null)
  const camera = useRef(null)
  const oreo = useRef(null)
  const oreoBlue = useRef(null)

  const cameraVelocity = useRef({x: 0, y: 0})
  const targetVelocity = useRef({x: 0, y: 0})
  const oreoVelocity = useRef({x: 0, y: 0})
  const gameState = useRef('idle')

  const [gameStatus, setGameStatus] = useState('idle')
  const isResettingPosition = useRef(false)
  const rings = useRef([])
  const time = useRef(0)
  const targetIntensity = useRef(0)
  const [user, setUser] = useState(() => {
    // Ambil user dari localStorage saat inisialisasi
    const savedUser = localStorage.getItem('user')
    return savedUser ? JSON.parse(savedUser) : null
  })
  const [email, setEmail] = useState('')
  const [redirectTo, setRedirectTo] = useState('')
  const [type, setType] = useState('')
  const [debugRotation, setDebugRotation] = useState({
    x: 73,
    y: -30,
    z: -150,
  })
  // const [debugSize, setDebugSize] = useState(1)
  // const [oreoScreenSize, setOreoScreenSize] = useState({width: 0, height: 0})
  const [showDebug, setShowDebug] = useState(false)
  const [showButtonDebug, setShowButtonDebug] = useState(false)
  const allowOreoMovement = useRef(false)
  const location = useLocation()
  const [showPopup, setShowPopup] = useState(false)
  const oreoOpacity = useRef(0)
  const fadeAnimationRef = useRef(null)
  const oreoScale = useRef(1)
  const scaleAnimationRef = useRef(null)
  const logoModels = useRef([])
  const logoDefaultModel = useRef(null)

  const currentLogo = useRef(null)
  const [isPlayBonusLevel, setIsPlayBonusLevel] = useState(false)
  const isPlayBonusLevelRef = useRef(false)
  const [displayedPoints, setDisplayedPoints] = useState({
    previous: 0,
    reward: 0,
  })
  const [showTextBonusLevel, setShowTextBonusLevel] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const oreoDistanceFromCamera = useRef(OREO_DISTANCE_FROM_CAMERA_IDLE)
  const wall = useRef(null)

  // Tambahkan state untuk debug logo
  const [debugLogo, setDebugLogo] = useState({
    position: {x: 0, y: 0, z: 0},
    scale: {x: 1, y: 1, z: 1},
    rotation: {x: Math.PI, y: Math.PI, z: Math.PI},
  })

  // Simpan referensi ke stars group
  const starsRef = useRef(null)

  const [showRingArea, setShowRingArea] = useState(false)
  const ringAreaBox = useRef(null)

  // Tambahkan ref untuk menyimpan ring yang sedang dalam transisi
  const hitRingTransitions = useRef({})

  // Tambahkan fungsi untuk menganimasikan transisi ring
  const animateHitRingTransition = (ringIndex: number) => {
    if (!hitRingTransitions.current[ringIndex]) return

    const transition = hitRingTransitions.current[ringIndex]
    transition.opacity += 0.05  // Kecepatan transisi

    if (transition.material) {
      transition.material.opacity = transition.opacity
    }

    if (transition.opacity < 1) {
      requestAnimationFrame(() => animateHitRingTransition(ringIndex))
    } else {
      // Hapus dari daftar transisi setelah selesai
      delete hitRingTransitions.current[ringIndex]
    }
  }

  const getUserLocalStorage = (key?: string) => {
    const userData = JSON.parse(
      localStorage.getItem('user') ?? '{"level": 0, "point": 0}'
    )
    return key ? userData[key] : userData
  }
  const setUserLocalStorage = (
    key: string,
    value: number | string | UserData | Date | boolean | number[]
  ) => {
    if (key === 'allValue') {
      localStorage.setItem('user', JSON.stringify(value))
    } else {
      const userData = getUserLocalStorage()
      userData[key] = value
      localStorage.setItem('user', JSON.stringify(userData))
    }
  }

  const sendDataToCMS = async (point: number) => {
    const user = getUserLocalStorage()
    const payload = {
      user_id: user?.id,
      hubspot_id: user?.hubspot_id,
      image: localStorage.getItem('photo'),
      region: user?.region,
      point,
      bypassStatus: 'active',
      bypassSubmission: point === 0 ? 'reject' : 'approve',
    }
    try {
      const response = await fetch(API_SEND_DATA_TO_CMS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'api_key': 'df0b673d5a625cf3b76233f9ba993873b321400895',
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        // console.log('Send data to CMS success')
      } else {
        // console.log('Send data to CMS failed')
      }
    } catch (error) {
      console.error('Error sending data to CMS:', error)
    }
  }

  useEffect(() => {
    // Ambil query parameter dari URL
    const queryParams = new URLSearchParams(location.search)
    const status = queryParams.get('status')

    // Cek apakah status adalah 'debug'
    if (status === 'debug') {
      setShowButtonDebug(true)  // Tampilkan debug jika status adalah 'debug'
    } else {
      setShowButtonDebug(false)  // Sembunyikan debug jika tidak
    }
  }, [location.search])

  function createStars() {
    if (!scene.current || !camera.current) return

    // Buat group khusus untuk bintang yang akan mengikuti kamera
    const starsGroup = new THREE.Group()
    scene.current.add(starsGroup)

    const starsGeometry = new THREE.BufferGeometry()
    const positions = []
    const velocities = []
    const sizes = []
    const opacities = []
    const speeds = []

    // Generate initial star positions
    for (let i = 0; i < STAR_COUNT; i++) {
      // Use minimum radius to avoid center clustering
      const minRadius = STAR_SPREAD_X * 0.3  // Minimum 30% of max spread
      const radius = minRadius + Math.random() * (STAR_SPREAD_X - minRadius)
      const theta = Math.random() * Math.PI * 2

      // Add some random offset to break up the cylindrical pattern
      const x = radius * Math.cos(theta) + (Math.random() - 0.5) * 2
      const y = radius * Math.sin(theta) + (Math.random() - 0.5) * 2
      const z = -Math.random() * STAR_SPREAD_Z

      positions.push(x, y, z)

      // Reduced velocity for slower movement
      velocities.push(
        0,  // x velocity
        0,  // y velocity
        Math.random() * 0.01 + 0.005  // z velocity (significantly reduced)
      )

      // Varied star sizes
      sizes.push(Math.random() * (MAX_STAR_SIZE - MIN_STAR_SIZE) + MIN_STAR_SIZE)

      // Adjusted opacity range
      opacities.push(Math.random() * 0.7 + 0.3)

      // Slower speed variation
      speeds.push(Math.random() * 1.5 + 0.5)
    }

    starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3))
    starsGeometry.setAttribute('velocity', new THREE.Float32BufferAttribute(velocities, 3))
    starsGeometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1))
    starsGeometry.setAttribute('opacity', new THREE.Float32BufferAttribute(opacities, 1))
    starsGeometry.setAttribute('speed', new THREE.Float32BufferAttribute(speeds, 1))

    const starsMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: {value: 0},
      },
      vertexShader: `
        attribute float size;
        attribute float opacity;
        attribute float speed;
        uniform float time;
        varying float vOpacity;
        varying vec2 vUv;
        
        void main() {
          vOpacity = opacity * (0.7 + 0.3 * sin(time * speed));
          vUv = uv;
          vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
          gl_PointSize = size * (300.0 / -mvPosition.z);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying float vOpacity;
        varying vec2 vUv;
        
        void main() {
          vec2 center = vec2(0.5, 0.5);
          vec2 coord = gl_PointCoord - center;
          float dist = length(coord);
          
          // Inti bintang
          float core = 1.0 - smoothstep(0.0, 0.2, dist);
          
          // Efek glow
          float glow = 1.0 - smoothstep(0.2, 0.5, dist);
          
          // Efek flare (sinar menyilang)
          float flareX = 0.5 - abs(coord.x);
          float flareY = 0.5 - abs(coord.y);
          float flare = pow(max(flareX, flareY), 3.0) * 0.5;

          // Efek sparkle (kilau)
          float sparkle = pow(1.0 - dist, 8.0) * 
                         (0.7 + 0.3 * sin(gl_PointCoord.x * 10.0) * sin(gl_PointCoord.y * 10.0));
          
          // Kombinasikan semua efek
          float brightness = core + glow * 0.5 + flare + sparkle;
          
          // Warna bintang dengan gradien dari putih ke biru muda
          vec3 color = mix(
            vec3(1.0, 1.0, 1.0),  // Putih di tengah
            vec3(0.8, 0.9, 1.0),   // Biru muda di tepi
            dist
          );
          
          if (dist > 0.5) discard;
          
          gl_FragColor = vec4(color, vOpacity * brightness);
        }
      `,
      transparent: true,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
    })

    const stars = new THREE.Points(starsGeometry, starsMaterial)
    starsGroup.add(stars)

    // Tambahkan fungsi untuk update posisi group bintang
    const updateStarsPosition = () => {
      if (!camera.current) return
      starsGroup.position.copy(camera.current.position)
    }

    // Kembalikan objek yang berisi stars dan fungsi update
    return {
      stars,
      updateStarsPosition,
    }
  }

  function updateStars(stars, time) {
    if (!stars) return

    const positions = stars.geometry.attributes.position.array
    const velocities = stars.geometry.attributes.velocity.array
    const speeds = stars.geometry.attributes.speed.array

    // Update positions based on velocities
    for (let i = 0; i < positions.length; i += 3) {
      // Move star forward (increasing z)
      positions[i + 2] += velocities[i + 2] * speeds[i / 3]

      // If star goes beyond camera, reset it to far end
      if (positions[i + 2] > 5) {
        // Reset to back with new random position using minimum radius
        const minRadius = STAR_SPREAD_X * 0.3
        const radius = minRadius + Math.random() * (STAR_SPREAD_X - minRadius)
        const theta = Math.random() * Math.PI * 2

        // Add random offset to position
        positions[i] = radius * Math.cos(theta) + (Math.random() - 0.5) * 2
        positions[i + 1] = radius * Math.sin(theta) + (Math.random() - 0.5) * 2
        positions[i + 2] = -STAR_SPREAD_Z
      }
    }

    // Update material uniforms for twinkling effect
    stars.material.uniforms.time.value = time

    // Mark position attribute as needing update
    stars.geometry.attributes.position.needsUpdate = true
  }

  const checkMissed = () => {
    if (oreo.current && oreo.current.position.z < -50) {
      oreo.current.position.z = 10  // hide oreo position
      hideOreo()  // sembunyikan oreo
      gameState.current = 'missed'
      targetIntensity.current = 0
      setGameStatus('missed')
      setDisplayedPoints({
        previous: getUserLocalStorage('point'),
        reward: 0,
      })
    }
  }
  const calculatePoints = (type: 'bonus' | 'standard' = 'standard') => {
    if (type === 'bonus') {
      return 6000
    }
    return 100  // Menghitung poin tetap
  }

  const refreshToken = async () => {
    try {
      const refreshToken = localStorage.getItem('refreshToken')
      if (!refreshToken) {
        throw new Error('No refresh token found')
      }

      const response = await fetch(API_REFRESH_TOKEN, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${refreshToken}`,
        },

      })

      const data = await response.json()

      if (response.ok) {
        // Update tokens dan user data di localStorage
        localStorage.setItem('token', data.data.token)
        localStorage.setItem('refreshToken', data.data.refreshToken)
        setUserLocalStorage('allValue', data.data.user)
        setUser(data.data.user)
        return data.data.token
      } else {
        throw new Error(data.message || 'Failed to refresh token')
      }
    } catch (error) {
      console.error('Error refreshing token:', error)
      // Hapus data user dan token jika refresh gagal
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      setUserLocalStorage('allValue', '')
      setUser(null)
      setGameStatus('login')
      gameState.current = 'login'
      throw error
    }
  }

  // set last play game to false first render
  useEffect(() => {
    localStorage.setItem('lastPlayGame', 'false')
  }, [])

  // Update fungsi submitPoints untuk menggunakan refresh token
  const submitPoints = async (
    point: number,
    type: 'bonus' | 'standard' = 'standard',
    level: number,
    oreo_cookie: number[]
  ) => {
    const cid = getCid()
    const utmParams = getUtmParams()

    const lastPlayGame =
      localStorage.getItem('lastPlayGame') !== 'false'

    const payload = {
      level,
      region: user?.region,
      collectedPoints: point,
      typePoint: type,
      cid,
      utmCampaign: utmParams.utmCampaign,
      utmContent: utmParams.utmContent,
      utmMedium: utmParams.utmMedium,
      utmSource: utmParams.utmSource,
      utmTerm: utmParams.utmTerm,
      oreoCookie: oreo_cookie,
      lastPlayGame,
    }

    // set last play game to true for next submit
    console.log('set last play game to true for next submit point')
    localStorage.setItem('lastPlayGame', 'true')

    try {
      const token = localStorage.getItem('token')

      const makeRequest = async (authToken: string) => {
        const response = await fetch(API_SUBMIT_FORM, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
          body: JSON.stringify(payload),
        })

        if (response.status === 401) {
          // Token expired, coba refresh
          const newToken = await refreshToken()
          // Retry dengan token baru
          return makeRequest(newToken)
        }

        const data = await response.json()
        // handle suspend
        if (
          ['User has suspended', 'User not found db'].includes(data.message)
        ) {
          handleSuspendOrDeletedUser()
          return
        }

        if (!response.ok) {
          throw new Error(data.message || 'Failed to submit points')
        }

        // console.log('Submit Form success:', data.message)
        return data
      }

      if (!token) {
        throw new Error('No token found')
      }

      if (type === 'bonus') {
        await sendDataToCMS(point)
      }

      return await makeRequest(token)
    } catch (error) {
      console.error('Error submitting points:', error)
      if (error instanceof Error && error.message === 'No token found') {
        setGameStatus('login')
        gameState.current = 'login'
      }
    }
  }
  // Fungsi untuk memposisikan Oreo kembali ke depan kamera
  const resetOreoPosition = () => {
    if (!oreo.current || !camera.current) return false

    const direction = new THREE.Vector3()
    camera.current.getWorldDirection(direction)
    const oreoDistance = 3.5

    // Posisi oreo di depan kamera
    const oreoPosition = camera.current.position
      .clone()
      .add(direction.multiplyScalar(oreoDistance))

    oreoPosition.y = 0.3

    // Set posisi oreo dengan lerp untuk animasi smooth
    oreo.current.position.lerp(oreoPosition, 0.1)

    // Reset rotasi ke posisi awal (90 derajat di sumbu X)
    oreo.current.rotation.x = (90 * Math.PI) / 180
    oreo.current.rotation.y = 0

    // Target rotasi Z (180 derajat)
    const targetRotationZ = (180 * Math.PI) / 180

    // Hitung progress berdasarkan jarak
    const startDistance = 10  // Jarak awal saat mulai reset
    const distanceToTarget = oreo.current.position.distanceTo(oreoPosition)
    const progress = 1 - distanceToTarget / startDistance

    // Rotasi Z berdasarkan progress (satu putaran penuh)
    oreo.current.rotation.z = progress * targetRotationZ

    // Reset kecepatan rotasi
    rotationIntensity.current = minRotationSpeed

    // Jika posisi sudah mendekati target, hentikan animasi
    if (distanceToTarget < 0.1) {
      oreo.current.rotation.z = targetRotationZ
      isResettingPosition.current = false
      return true
    }
    return false
  }

  const onTarget = () => {
     soundOnTarget.current?.play()

     if (!oreo.current) return
     const currentLevel = getUserLocalStorage('level') + 1

     // Set flag bahwa sedang dalam proses reset posisi
     isResettingPosition.current = true

     const userData = getUserLocalStorage()

     if (isPlayBonusLevelRef.current) {
       changeOreo('Base_blue', 'Shooting_Star')
       const bonusPoints = calculatePoints('bonus')
       const newPoints = (userData.point ?? 0) + bonusPoints
       const now = new Date()

       setDisplayedPoints({previous: userData.point, reward: bonusPoints})

       // Update storage dan state sekaligus
       const updatedUser = {
         ...userData,
         point: newPoints,
         latest_usage_bonus_level: now,
         allow_bonus_level: false,
         is_success_bonus: true,
       }
       setUserLocalStorage('allValue', updatedUser)
       setUser(updatedUser)

       gameState.current = 'congratulation'
       setGameStatus('congratulation')
     } else {
       changeOreo('Base_blue', getOreoNameMesh(currentLevel))
       setDisplayedPoints({
         previous: userData.point,
         reward: calculatePoints(),
       })
       const newLevel = (userData.level ?? 0) + 1
       const newPoints = (userData.point ?? 0) + calculatePoints()
       setUserLocalStorage('level', newLevel)
       setUserLocalStorage('point', newPoints)
       setUser(prevUser => (prevUser
         ? {
           ...prevUser,
           level: newLevel,
           point: newPoints,
         }
         : null))
       gameState.current = 'congratulation'
       setGameStatus('congratulation')
     }

     // Jalankan animasi reset posisi
     const animate = () => {
       if (resetOreoPosition()) {
         // Animasi selesai, lanjutkan dengan flow normal
         targetIntensity.current = 0
         const oreo_cookie = getUserLocalStorage('oreo_cookie')
         if (getUserLocalStorage('status') !== 'firstTimePlaying') {
           if (isPlayBonusLevelRef.current) {
             submitPoints(
               calculatePoints('bonus'),
               'bonus',
               getUserLocalStorage('level'),
               oreo_cookie
             )
           } else {
             submitPoints(
               calculatePoints(),
               'standard',
               getUserLocalStorage('level'),
               oreo_cookie
             )
           }
         }

         setTimeout(() => {
           clearRings()

           if (!isPlayBonusLevelRef.current) {
             setGameStatus('level-up')
             gameState.current = 'level-up'
           }
           setTimeout(() => {
             hideOreo()
           }, 2000)
           setTimeout(() => {
             const level = getUserLocalStorage('level')
             if (getUserLocalStorage('status') === 'firstTimePlaying') {
               setGameStatus('result')
               gameState.current = 'result'
             } else if (isPlayBonusLevelRef.current) {
               setIsPlayBonusLevel(false)
               isPlayBonusLevelRef.current = false
               setGameStatus('result')
               gameState.current = 'result'
             } else if (level === 4) {
               setGameStatus('result')
               gameState.current = 'result'
             } else {
               playAgain()
               return
             }

             setTimeout(() => {
               const userData = getUserLocalStorage()
               if (
                 userData.level === 4 &&
                gameState.current === 'result' &&
                userData.allow_bonus_level
               ) {
                 setShowPopup(true)
               }
             }, 3000)
           }, 3000)
         }, 3000)
       } else if (isResettingPosition.current) {
         // Lanjutkan animasi jika masih dalam proses reset
         requestAnimationFrame(animate)
       }
     }

     // Mulai animasi
     animate()
  }

  function checkCollision() {
    if (!oreo.current) return

    const oreoPos = new THREE.Vector3()
    oreo.current.getWorldPosition(oreoPos)

    rings.current.forEach((ring, index) => {
      const ringPos = new THREE.Vector3()
      ring.getWorldPosition(ringPos)

      const distance = oreoPos.distanceTo(ringPos)
      if (distance < 1.5) {
        // Buat texture baru untuk hit ring
        const svgBlob = new Blob([hitRingSvg], {type: 'image/svg+xml'})
        const svgUrl = URL.createObjectURL(svgBlob)

        if (ring instanceof THREE.Sprite) {
          const newMaterial = new THREE.SpriteMaterial({
            map: new THREE.TextureLoader().load(svgUrl, () => {
              URL.revokeObjectURL(svgUrl)
            }),
            transparent: true,
            opacity: 0,  // Mulai dengan opacity 0
          })

          // Simpan material lama
          const oldMaterial = ring.material

          // Set material baru
          ring.material = newMaterial

          // Tambahkan ke daftar transisi
          hitRingTransitions.current[index] = {
            opacity: 0,
            material: newMaterial,
          }

          // Mulai animasi
          animateHitRingTransition(index)

          // Hapus material lama setelah transisi selesai
          setTimeout(() => {
            if (oldMaterial) {
              oldMaterial.dispose()
            }
          }, 1000)
        }

        onTarget()
      }
    })

    checkMissed()
  }
  // Tambahkan rotationIntensity di level komponen
  const rotationIntensity = useRef(0.01)
  const minRotationSpeed = 0.01
  const maxRotationSpeed = 0.1
  const intensityIncrement = 0.005

  function updateOreoMovement() {
    if (!oreo.current || !camera.current) return

    // Jika sedang dalam proses reset posisi, skip update movement
    if (isResettingPosition.current) return

    if (gameState.current === 'shooting') {
      if (!allowOreoMovement.current) return
      const direction = new THREE.Vector3()
      camera.current.getWorldDirection(direction)

      // Normalisasi arah untuk konsistensi kecepatan
      direction.normalize()

      // Gerakkan Oreo dalam arah kamera
      oreo.current.position.x +=
        direction.x * OREO_SPEED + oreoVelocity.current.x
      oreo.current.position.y +=
        direction.y * OREO_SPEED + oreoVelocity.current.y
      oreo.current.position.z += direction.z * OREO_SPEED

      // Tingkatkan intensity secara bertahap
      rotationIntensity.current = Math.min(
        rotationIntensity.current + intensityIncrement,
        maxRotationSpeed
      )

      // Terapkan rotasi dengan intensity yang sudah diupdate
      oreo.current.rotation.z += rotationIntensity.current
    } else if (['aiming', 'idle', 'login'].includes(gameState.current)) {
      if (!camera.current) return
      const verticalOffset = 0
      const oreoDistance = oreoDistanceFromCamera.current
      const direction = new THREE.Vector3()
      camera.current.getWorldDirection(direction)

      // Posisi oreo di depan kamera
      const oreoPosition = camera.current.position
        .clone()
        .add(direction.multiplyScalar(oreoDistance))

      if (gameState.current === 'idle') {
        if (countryRef.current === 'id') {
          oreoPosition.y += -0.8
        } else if (countryRef.current === 'th') {
          oreoPosition.y += -1
        } else {
          oreoPosition.y += 1
        }
      }

      // Tambahkan offset ke bawah
      oreoPosition.y += verticalOffset
      // Set posisi oreo
      oreo.current.position.copy(oreoPosition)

      // Reset intensity ke minimum saat tidak shooting
      rotationIntensity.current = minRotationSpeed

      // Terapkan rotasi dengan kecepatan minimum
      oreo.current.rotation.z += rotationIntensity.current
    }
  }

  function createOreo(): Promise<void> {
    return new Promise((resolve, reject) => {
      const loader = new THREE.GLTFLoader()
      if (!scene.current) {
        reject(new Error('Scene not initialized'))
        return
      }

      loader.load(
        oreoModel,
        async (gltf) => {
          try {
            oreo.current = gltf.scene
            scene.current?.add(oreo.current)
            oreo.current.scale.set(2, 2, 2)
            changeOreo('Base', 'Oreo_default')
            // Atur rotasi awal
            oreo.current.rotation.x = (90 * Math.PI) / 180
            setDebugRotation({x: 90, y: 0, z: 0})

            // Set ukuran awal
            handleSizeChange(debugSize)

            resolve()
          } catch (error) {
            reject(error)
          }
        },
        undefined,
        (error) => {
          console.error('Terjadi kesalahan saat memuat model Oreo:', error)
          reject(error)
        }
      )
    })
  }

  const ringSvg = `
 <svg width="99" height="99" viewBox="0 0 99 99" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="49.7238" cy="49.5746" r="48" stroke="white" stroke-width="10" stroke-dasharray="8 8"/>
</svg>

  `

  const hitRingSvg = `
<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M50 100C56.5661 100 63.0679 98.7067 69.1342 96.194C75.2004 93.6812 80.7124 89.9983 85.3553 85.3553C89.9983 80.7124 93.6812 75.2004 96.194 69.1342C98.7067 63.0679 100 56.5661 100 50C100 43.4339 98.7067 36.9321 96.194 30.8658C93.6812 24.7995 89.9983 19.2876 85.3553 14.6447C80.7124 10.0017 75.2004 6.31876 69.1342 3.80602C63.0679 1.29329 56.5661 -9.78424e-08 50 0C36.7392 1.97602e-07 24.0215 5.26784 14.6447 14.6447C5.26784 24.0215 0 36.7392 0 50C0 63.2608 5.26784 75.9785 14.6447 85.3553C24.0215 94.7321 36.7392 100 50 100ZM48.7111 70.2222L76.4889 36.8889L67.9556 29.7778L44.0667 58.4389L31.7056 46.0722L23.85 53.9278L40.5167 70.5944L44.8167 74.8944L48.7111 70.2222Z" fill="white"/>
</svg>
  `

  function createRings(type?: string) {
    if (!scene.current) return
    rings.current.forEach(ring => scene.current?.remove(ring))
    rings.current = []

    // Set jumlah ring berdasarkan tipe
    let ringCount = 3  // Default jumlah ring untuk semua level
    if (type === 'bonus') {
      ringCount = 1  // Bonus level hanya 1 ring
    }

    // Definisikan area untuk penempatan ring
    const minDistance = 6  // Jarak minimum antar ring
    const areaSizeX = RING_AREA.width
    const areaSizeY = RING_AREA.height

    // Definisikan layer kedalaman
    const minDepth = Math.abs(RING_AREA.minZ) - RING_AREA.depth
    const maxDepth = Math.abs(RING_AREA.minZ)
    const layerCount = Math.min(3, ringCount)
    const layerSpacing = (maxDepth - minDepth) / layerCount

    // Array untuk menyimpan posisi yang sudah digunakan
    const usedPositions: { x: number; y: number; z: number }[] = []

    for (let i = 0; i < ringCount; i++) {
      // Buat SVG texture
      const svgBlob = new Blob([ringSvg], {type: 'image/svg+xml'})
      const svgUrl = URL.createObjectURL(svgBlob)
      const ringTexture = new THREE.TextureLoader().load(svgUrl, () => {
        URL.revokeObjectURL(svgUrl)
      })
      const spriteMaterial = new THREE.SpriteMaterial({
        map: ringTexture,
        transparent: true,
      })
      const ring = new THREE.Sprite(spriteMaterial)

      // Sesuaikan ukuran sprite
      ring.scale.set(2, 2, 1)

      // Fungsi untuk mengecek jarak dengan ring lain
      const checkDistance = (pos: { x: number; y: number; z: number }) => !usedPositions.some((usedPos) => {
        const dx = pos.x - usedPos.x
        const dy = pos.y - usedPos.y
        // Abaikan pengecekan z karena sudah diatur per layer
        const distance = Math.sqrt(dx * dx + dy * dy)
        return distance < minDistance
      })

      // Coba cari posisi yang valid
      let position
      let attempts = 0
      const maxAttempts = 100

      // Tentukan layer untuk ring ini
      const layer = i % layerCount
      const baseZ = -(minDepth + layer * layerSpacing)
      const zVariation = layerSpacing * 0.3  // Variasi dalam layer

      do {
        // Generate posisi acak dalam area yang ditentukan
        const padding = minDistance / 2
        position = {
          x:
            Math.random() * (areaSizeX - padding * 2) -
            (areaSizeX - padding * 2) / 2,
          y:
            Math.random() * (areaSizeY - padding * 2) -
            (areaSizeY - padding * 2) / 2,
          z: baseZ - Math.random() * zVariation,
        }

        attempts++
      } while (!checkDistance(position) && attempts < maxAttempts)

      // Terapkan posisi ke ring
      ring.position.set(position.x, position.y, position.z)

      // Simpan posisi yang digunakan
      usedPositions.push({
        x: ring.position.x,
        y: ring.position.y,
        z: ring.position.z,
      })

      rings.current.push(ring)
      scene.current.add(ring)
    }
  }

  function clearRings() {
    if (!scene.current) return

    // Menghapus semua ring yang ada di scene
    rings.current.forEach((ring) => {
      scene.current?.remove(ring)
      if (ring instanceof THREE.Sprite) {
        ring.material.dispose()
      }
    })

    // Mengosongkan array rings
    rings.current = []
  }

  function cameraMovement(time: number, intensity: number) {
    if (!camera.current) return
    const amplitude = 0.1 * intensity  // Amplitudo naik-turun
    // const frequency = 0.02; // Frekuensi
    const lateralAmplitude = 0.1 * intensity  // Amplitudo kiri-kanan

    // Gerakan naik-turun
    camera.current.position.y = amplitude * Math.sin(time)

    // Gerakan kiri-kanan
    camera.current.position.x = lateralAmplitude * Math.sin(time * 2)

    // Kamera tetap menghadap ke depan
    camera.current.lookAt(0, 0, 0)
  }

  const handleRotationChange = (axis: 'x' | 'y' | 'z', value: number) => {
    setDebugRotation(prev => ({
      ...prev,
      [axis]: value,
    }))

    if (oreo.current) {
      oreo.current.rotation[axis] = (value * Math.PI) / 180  // Konversi ke radian
    }
  }

  const handleSizeChange = (value: number) => {
    setDebugSize(value)

    if (oreo.current) {
      // Traverse semua mesh dalam model
      oreo.current.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          const {geometry} = child

          // Dapatkan vertices asli jika belum disimpan
          if (!geometry.userData.originalPositions) {
            const positions = geometry.attributes.position.array
            geometry.userData.originalPositions = Float32Array.from(positions)
          }

          // Modifikasi posisi vertices berdasarkan ukuran baru
          const {originalPositions} = geometry.userData
          const positions = geometry.attributes.position.array

          for (let i = 0; i < positions.length; i++) {
            positions[i] = originalPositions[i] * value
          }

          geometry.attributes.position.needsUpdate = true
          geometry.computeBoundingSphere()
          geometry.computeBoundingBox()
        }
      })
    }
  }

  // Tambahkan fungsi untuk fade in/out dengan animasi smooth
  const fadeOreo = (targetOpacity: number, duration: number = 1000) => {
    if (fadeAnimationRef.current) {
      cancelAnimationFrame(fadeAnimationRef.current)
    }

    const startOpacity = oreoOpacity.current
    const startTime = Date.now()

    const animate = () => {
      const currentTime = Date.now()
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)

      const easeProgress =
        progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2

      const newOpacity =
        startOpacity + (targetOpacity - startOpacity) * easeProgress
      oreoOpacity.current = newOpacity

      if (oreo.current) {
        oreo.current.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            // Jika targetOpacity adalah 0, hanya proses mesh Base dan Oreo_default
            if (targetOpacity === 0) {
              if (['Base', 'Oreo_default'].includes(child.name)) {
                child.material.transparent = newOpacity < 1
                child.material.opacity = newOpacity
              }
            }
            // Jika targetOpacity adalah 1, hanya proses mesh Base dan Oreo_default
            else if (targetOpacity === 1) {
              if (['Base', 'Oreo_default'].includes(child.name)) {
                child.material.transparent = newOpacity < 1
                child.material.opacity = newOpacity
              }
            }
          }
        })
      }

      if (progress < 1) {
        fadeAnimationRef.current = requestAnimationFrame(animate)
      } else {
        // Ketika animasi selesai, pastikan material diatur dengan benar
        if (oreo.current) {
          oreo.current.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              if (
                ['Base', 'Oreo_default'].includes(child.name) &&
                targetOpacity === 1
              ) {
                // Tampilkan hanya Base dan Oreo_default jika targetOpacity = 1

                child.material.transparent = false
                child.material.opacity = 1
              } else {
                // Mesh lainnya selalu transparan
                child.material.transparent = true
                child.material.opacity = 0
              }
            }
          })
        }
      }
    }

    fadeAnimationRef.current = requestAnimationFrame(animate)
  }

  useEffect(() => () => {
    if (fadeAnimationRef.current) {
      cancelAnimationFrame(fadeAnimationRef.current)
    }
  }, [])

  type MeshType =
    | 'Oreo_default'
    | 'Rocket'
    | 'Shooting_Star'
    | 'Star'
    | 'Teleskop'
    | 'Helmet';
  const getOreoNameMesh = (level: number) => {
    console.log('🚀 ~ getOreoNameMesh ~ level:', level)

    const getNameFromOreoPosition = (index: number): MeshType => {
      switch (index) {
        case 0:
          return 'Rocket'
        case 1:
          return 'Teleskop'
        case 2:
          return 'Helmet'
        case 3:
          return 'Star'
        default:
          return 'Oreo_default'
      }
    }
    const oreo_cookie = getUserLocalStorage('oreo_cookie')
    const oreoNames = {
      1: getNameFromOreoPosition(oreo_cookie[0] ?? 0),
      2: getNameFromOreoPosition(oreo_cookie[1] ?? 0),
      3: getNameFromOreoPosition(oreo_cookie[2] ?? 0),
      4: getNameFromOreoPosition(oreo_cookie[3] ?? 0),
    }
    const name = oreoNames[level as keyof typeof oreoNames]
    console.log('🚀 ~ getOreoNameMesh ~ name:', name)

    return name
  }

  const changeOreo = (color: 'Base' | 'Base_blue', logo: MeshType) => {
    oreo.current?.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.name === color || child.name === logo) {
          child.material.transparent = false
          child.material.opacity = 1
        } else {
          child.material.transparent = true
          child.material.opacity = 0
        }
      }
    })
  }

  // Update fungsi showOreo dan hideOreo yang sudah ada
  const showOreo = () => {
    changeOreo('Base', 'Oreo_default')

    scaleUp()
  }
  const hideOreo = () => scaleDown()

  const fadeAnimationRefOreoBlue = useRef(null)
  const oreoOpacityBlue = useRef(0)

  const fadeOreoBlue = (targetOpacity: number, duration: number = 1000) => {
    if (fadeAnimationRefOreoBlue.current) {
      cancelAnimationFrame(fadeAnimationRefOreoBlue.current)
    }

    const startOpacity = oreoOpacityBlue.current
    const startTime = Date.now()

    const animate = () => {
      const currentTime = Date.now()
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)

      const easeProgress =
        progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2

      const newOpacity =
        startOpacity + (targetOpacity - startOpacity) * easeProgress
      oreoOpacityBlue.current = newOpacity

      if (oreoBlue.current) {
        oreoBlue.current.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            // Set transparent hanya jika opacity < 1
            child.material.transparent = newOpacity < 1
            child.material.opacity = newOpacity
          }
        })
      }

      if (progress < 1) {
        fadeAnimationRefOreoBlue.current = requestAnimationFrame(animate)
      } else {
        // Ketika animasi selesai, pastikan material diatur dengan benar
        if (oreoBlue.current && targetOpacity === 1) {
          oreoBlue.current.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              child.material.transparent = false
              child.material.opacity = 1
            }
          })
        }
      }
    }

    fadeAnimationRefOreoBlue.current = requestAnimationFrame(animate)
  }

  const hideOreoBlue = () => fadeOreoBlue(0)

  // Tambahkan fungsi untuk animasi scale
  const scaleOreo = (targetScale: number, duration: number = 1000) => {
    if (scaleAnimationRef.current) {
      cancelAnimationFrame(scaleAnimationRef.current)
    }

    const startScale = oreoScale.current
    const startTime = Date.now()

    const animate = () => {
      const currentTime = Date.now()
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)

      const easeProgress =
        progress < 0.5
          ? 2 * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 2) / 2

      const newScale = startScale + (targetScale - startScale) * easeProgress
      oreoScale.current = newScale

      if (oreo.current) {
        oreo.current.scale.set(newScale, newScale, newScale)
      }

      if (progress < 1) {
        scaleAnimationRef.current = requestAnimationFrame(animate)
      }
    }

    scaleAnimationRef.current = requestAnimationFrame(animate)
  }

  // Tambahkan fungsi untuk scale up dan down
  const scaleUp = () => scaleOreo(2)
  const scaleDown = () => scaleOreo(0)

  // Tambahkan cleanup untuk animasi scale
  useEffect(() => () => {
    if (scaleAnimationRef.current) {
      cancelAnimationFrame(scaleAnimationRef.current)
    }
  }, [])

  function setupCameraAndRenderer(
    camera: any,
    renderer: any,
    oreo: any,
    status: any
  ) {
    // Atur rasio aspek
    if (status === 'idle') {
      const aspectRatio = window.innerWidth / 1120
      camera.aspect = aspectRatio  // Atur aspek kamera
      camera.fov = 75  // Atur FOV sesuai kebutuhan
      camera.updateProjectionMatrix()  // Perbarui matriks proyeksi
      camera.position.set(0, 0, 40)

      oreoDistanceFromCamera.current = OREO_DISTANCE_FROM_CAMERA_IDLE
      if (wall.current) {
        wall.current.position.z = -45
      }
      // Atur ukuran renderer
      renderer.setSize(window.innerWidth, 1120)  // Atur ukuran renderer
      renderer.setPixelRatio(window.devicePixelRatio)  // Sesuaikan dengan rasio piksel perangkat
    } else {
      const aspectRatio = window.innerWidth / window.innerHeight
      camera.aspect = aspectRatio  // Atur aspek kamera
      camera.fov = 75  // Atur FOV sesuai kebutuhan
      camera.updateProjectionMatrix()  // Perbarui matriks proyeksi
      camera.position.set(0, 0, 5)
      oreoDistanceFromCamera.current = OREO_DISTANCE_FROM_CAMERA_NORMAL
      if (wall.current) {
        wall.current.position.z = -70
      }
      // Atur ukuran renderer
      renderer.setSize(window.innerWidth, window.innerHeight)  // Atur ukuran renderer
      renderer.setPixelRatio(window.devicePixelRatio)  // Sesuaikan dengan rasio piksel perangkat
    }
  }

  useEffect(() => {
    const init = async () => {
      scene.current = new THREE.Scene()
      camera.current = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        1000
      )
      renderer.current = new THREE.WebGLRenderer({antialias: true})
      renderer.current.setClearColor(0xffffff)
      renderer.current.setSize(window.innerWidth, window.innerHeight)

      // Attach renderer to container
      containerRef.current?.appendChild(renderer.current.domElement)

      // Tembok Galaxy environment
      const scaleWall = 0.7
      const wallGeometry = new THREE.BoxGeometry(
        108 * scaleWall,
        192 * scaleWall,
        1
      )
      const wallTexture = new THREE.TextureLoader().load(backgroundTexture)

      wallTexture.colorSpace = THREE.SRGBColorSpace  // Ensure correct color space
      wallTexture.needsUpdate = true

      const wallMaterial = new THREE.MeshBasicMaterial({
        map: wallTexture,
      })

      wall.current = new THREE.Mesh(wallGeometry, wallMaterial)
      wall.current.position.z = -40
      scene.current.add(wall.current)

      camera.current.position.z = 5

      const ambientLight = new THREE.AmbientLight(0xffffff, 1)
      scene.current.add(ambientLight)

      const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
      directionalLight.position.set(0, 1, 1)
      directionalLight.shadow.bias = 0.0001  // Adjust shadow bias

      scene.current.add(directionalLight)

      await createOreo()
      starsRef.current = createStars()

      // Event listeners
      window.addEventListener('resize', onWindowResize, false)

      //   startTimer();

      // Buat ring area box
      ringAreaBox.current = createRingAreaBox()
      if (ringAreaBox.current) {
        ringAreaBox.current.visible = showRingArea
        scene.current?.add(ringAreaBox.current)
      }

      // const bgm = new Audio(bgmTrack)
      // bgm.loop = true
      // bgm.volume = 0.3
      // bgm.play().catch((error) => {
      //   document.addEventListener('click', () => {
      //     bgm.play().catch(console.error)
      //   }, {once: true})
      // })

      setIsLoading(false)
    }

    const onWindowResize = () => {
      // console.log('hehe')
      if (!camera.current || !renderer.current) return
      camera.current.aspect = window.innerWidth / window.innerHeight
      camera.current.updateProjectionMatrix()
      renderer.current.setSize(window.innerWidth, window.innerHeight)
    }

    let intensity = 0
    // const targetIntensity = 1; // Intensitas target
    const lerpFactor = 0.01  // Kehalusan interpolasi
    const animate = () => {
      requestAnimationFrame(animate)
      time.current += 0.02
      if (camera.current && renderer.current && oreo.current) {
        setupCameraAndRenderer(
          camera.current,
          renderer.current,
          oreo.current,
          gameState.current
        )
      }
      // Tambahkan kembali update intensity
      intensity += (targetIntensity.current - intensity) * lerpFactor

      // Update posisi bintang relatif terhadap kamera
      if (starsRef.current) {
        starsRef.current.updateStarsPosition()
        updateStars(starsRef.current.stars, time.current)
      }

      if (camera.current) {
        updateOreoMovement()
        cameraMovement(time.current, intensity)
      }

      if (gameState.current === 'shooting') {
        checkCollision()
      }

      if (scene.current && camera.current && renderer.current) {
        renderer.current.render(scene.current, camera.current)
      }
    }
    animate()

    // Initialize the scene
    init()

    return () => {
      // Cleanup listeners and Three.js resources
      window.removeEventListener('resize', onWindowResize)
      //   document.getElementById('shootButton')?.removeEventListener('click', shootOreo);
      renderer.current?.dispose()
    }
  }, [])

  // Tambahkan effect untuk mengontrol visibilitas ring area box
  useEffect(() => {
    if (ringAreaBox.current) {
      ringAreaBox.current.visible = showRingArea
    }
  }, [showRingArea])
  const onMove = (
    targetVel: { x: number; y: number },
    oreoVel: { x: number; y: number }
  ) => {
    // console.log('onMove')
    if (gameState.current === 'aiming') {
      targetVelocity.current = targetVel
    } else {
      oreoVelocity.current = oreoVel
    }
  }
  const onEnd = () => {
    // console.log('onEnd')
    if (gameState.current === 'aiming') {
      targetVelocity.current.x = 0
      targetVelocity.current.y = 0
    } else {
      oreoVelocity.current.x *= DECELERATION
      oreoVelocity.current.y *= DECELERATION
    }
  }

  const onTimeUp = () => {
    gameState.current = 'time-out'
    setGameStatus('time-out')
    targetIntensity.current = 0
  }

  const getOreoName = ():string => {
    if (isPlayBonusLevel) return translate('getOreoName_OreoShootingStar', country as Language)

    const getNameFromOreoPosition = (index: number) => {
      switch (index) {
        case 0:
          return translate('getOreoName_OreoRocket', country as Language)
        case 1:
          return translate('getOreoName_OreoTelescope', country as Language)
        case 2:
          return translate('getOreoName_OreoAstronaut', country as Language)
        case 3:
          return translate('getOreoName_OreoStarburst', country as Language)
      }
    }
    const oreoNames = {
      1: getNameFromOreoPosition(user?.oreo_cookie[0] ?? 0),
      2: getNameFromOreoPosition(user?.oreo_cookie[1] ?? 0),
      3: getNameFromOreoPosition(user?.oreo_cookie[2] ?? 0),
      4: getNameFromOreoPosition(user?.oreo_cookie[3] ?? 0),
    }

    return (
      oreoNames[getUserLocalStorage('level') as keyof typeof oreoNames] ||
       translate('getOreoName_OreoStarburst', country as Language)
    )
  }

  let RenderTitle
  if (['aiming', 'shooting'].includes(gameStatus)) {
    // RenderTitle = <Title text='OREO MILKWAY DUNK' className='mt-6 fade-in'/>
  } else if (gameStatus === 'congratulation') {
    RenderTitle = <Title text={translate('congratulations_title', country as Language)} className='title-mt fade-in '/>
  } else if (gameStatus === 'time-out') {
    RenderTitle = <Title text={translate('timeOut', country as Language)} className='mt-6 fade-in '/>
  } else if (gameStatus === 'missed') {
    RenderTitle = <Title text={translate('missedTheTarget', country as Language)} className='mt-6 fade-in '/>
  } else if (gameStatus === 'result') {
    RenderTitle = <Title text={translate('theResult', country as Language)} className='mt-6 fade-in' />
  } else if (gameStatus === 'level-up') {
    const userLevel = user?.level ?? 0  // Jika user null, gunakan 0 sebagai default
    RenderTitle = (
      <Title text={translateLevelUp(country as Language, userLevel)} className='title-mt fade-in' />
    )
  } else if (gameStatus === 'tutorial') {
    RenderTitle = (
      <Title text={translate('gameTutorial_Title', country as Language)} className='mt-6 fade-in' />
    )
  }

  let RenderDesc
  if (['congratulation', 'level-up'].includes(gameStatus)) {
    RenderDesc = (
      <>
        <div className='flex flex-col items-center justify-center fade-in mt-1'>
          <p className='text-center'>
            {translatePointsEarned(country as Language, displayedPoints.reward)}
          </p>
          {/* <CurvedText text={getOreoName()} curvature={140} position="below" /> */}
        </div>
      </>
    )
  } else if (gameStatus === 'missed') {
    RenderDesc = (
      <p className='fade-in text-center'>
        {translate('youFailedToHitTheTarget', country as Language)}
      </p>
    )
  }

  let RenderTimer
  if (['shooting'].includes(gameStatus) && !isPlayBonusLevel) {
    RenderTimer = <Timer duration={60} className='mt-4' onTimeUp={onTimeUp} />
  }

  const resetLevelAndGenerateCookiePosition = () => {
    if (getUserLocalStorage('level') >= MAX_LEVEL) {
      console.log('reset level and generate cookie position')
      setUser(prevUser => (prevUser ? {...prevUser, level: 0} : null))
      setUserLocalStorage('level', 0)
      generateCookiePosition()
    } else if (getUserLocalStorage('oreo_cookie') === null) {
      console.log('generateCookiePosition')
      generateCookiePosition()
    }
  }

  const generateCookiePosition = () => {
    const initialArray = getUserLocalStorage('oreo_cookie') || [0, 1, 2, 3, 4]
    const prevArray = [...initialArray]
    const newArray = shuffleArrayWithRules(initialArray, prevArray)

    setUserLocalStorage('oreo_cookie', newArray)
    setUser(prevUser => (prevUser ? {...prevUser, oreo_cookie: newArray} : null))

    // hanya store level dan urutan cookie aja
    submitPoints(0, 'standard', 0, newArray)
  }

  const playAgain = () => {
    if (showTextBonusLevel) setShowTextBonusLevel(false)
    resetLevelAndGenerateCookiePosition()
    setGameStatus('aiming')
    gameState.current = 'aiming'
    targetIntensity.current = 1
    createRings()
    // loadModel()
    showOreo()
  }

  const playAgainBonusLevel = () => {
    setGameStatus('aiming')
    gameState.current = 'aiming'
    targetIntensity.current = 1
    createRings('bonus')
    // loadModel()
    showOreo()
  }

  const playBonusLevel = () => {
    setIsPlayBonusLevel(true)
    isPlayBonusLevelRef.current = true
    setGameStatus('aiming')
    gameState.current = 'aiming'
    targetIntensity.current = 1
    createRings('bonus')
    setShowTextBonusLevel(true)
    showOreo()
    // loadModel()
  }

  const goToResultBonusLevel = () => {
    const userData = getUserLocalStorage()
    submitPoints(
      0,
      'bonus',
      getUserLocalStorage('level'),
      getUserLocalStorage('oreo_cookie')
    )

    const now = new Date()
    // Update storage dan state sekaligus
    const updatedUser = {
      ...userData,
      latest_usage_bonus_level: now,
      allow_bonus_level: false,
      is_success_bonus: false,
    }
    setUserLocalStorage('allValue', updatedUser)
    setUser(updatedUser)

    setIsPlayBonusLevel(false)
    isPlayBonusLevelRef.current = false
    setGameStatus('result')
    gameState.current = 'result'
  }

  const goToResult = () => {
    if (isPlayBonusLevel) {
      setIsPlayBonusLevel(false)
      isPlayBonusLevelRef.current = false
    }
    setGameStatus('result')
    gameState.current = 'result'
  }

  const firstTimePlaying = () => {
    if (!country) {
      alert('Select country first')
      return
    }
    const newUser = {
      status: 'firstTimePlaying',
      level: 0,
      point: 0,
      id: 0,
      hubspot_id: '',
      first_name: '',
      last_name: '',
      username: '',
      email: '',
      phone: '',
      latest_usage_bonus_level: null,
      is_active: false,
      created_at: '',
      updated_at: '',
      allow_bonus_level: false,
      is_success_bonus: false,
      region: country?.toLowerCase(),
      oreo_cookie: [0, 1, 2, 3, 4],
    }
    setUser(newUser)
    setUserLocalStorage('allValue', newUser)
    localStorage.setItem('token', '')
    localStorage.setItem('refreshToken', '')

    setGameStatus('tutorial')
    gameState.current = 'tutorial'
  }

  const closeTutorial = () => {
    setGameStatus('aiming')
    gameState.current = 'aiming'
    targetIntensity.current = 1
    createRings()
  }

  const goToLeaderboard = () => {
    if (getUserLocalStorage('status') === 'firstTimePlaying') {
      setGameStatus('action-need')
      gameState.current = 'action-need'
    } else {
      setGameStatus('leaderboard')
      gameState.current = 'leaderboard'
    }
  }
  // Fungsi untuk mengarahkan ke region yang sesuai
  const redirectToRegion = (userRegion: string, currentRegion: string) => {
    if (userRegion !== currentRegion?.toLocaleLowerCase()) {
      history.push(`${base}/${userRegion}`)
    }
  }
  // Callback untuk menangani login
  const handleLogin = (userData: UserData) => {
    // allow bonus level
    userData.allow_bonus_level = userData.latest_usage_bonus_level
      ? new Date().getTime() -
          new Date(userData.latest_usage_bonus_level).getTime() >
        2 * 24 * 60 * 60 * 1000
      : true

    setUser(userData)
    resetLevelAndGenerateCookiePosition()
    setGameStatus('aiming')
    gameState.current = 'aiming'
    targetIntensity.current = 1
    createRings()
    showOreo()
    redirectToRegion(userData.region, country)
  }

  const handleRegis = (email: string) => {
    setType('register')
    setEmail(email)
    setRedirectTo('leaderboard')
    setGameStatus('update-login-pin')
    gameState.current = 'update-login-pin'
  }
  const handleUpdateLoginPin = async (userData?: UserData) => {
    setType('')
    setGameStatus(redirectTo)
    gameState.current = redirectTo
    if (redirectTo === 'leaderboard' && userData) {
      setUser(userData)
      // targetIntensity.current = 1;
      // createRings();
    }
    setRedirectTo('')
  }

  const handleBackToLogin = () => {
    setGameStatus('login')
    gameState.current = 'login'
  }

  const handleForgotSuccess = (email: string) => {
    setType('forgotPassword')
    setEmail(email)
    setRedirectTo('login')
    setGameStatus('update-login-pin')
    gameState.current = 'update-login-pin'
  }

  const handleForgotPassword = () => {
    setGameStatus('forgot-password')
    gameState.current = 'forgot-password'
  }

  const goToLogin = () => {
    setGameStatus('login')
    gameState.current = 'login'
  }

  const [debugSize, setDebugSize] = useState(1)

  // Tambahkan fungsi untuk membuat wireframe box
  function createRingAreaBox() {
    if (!scene.current) return null

    // Buat geometri box sesuai dengan area ring
    const geometry = new THREE.BoxGeometry(
      RING_AREA.width,
      RING_AREA.height,
      RING_AREA.depth
    )

    // Buat material
    const material = new THREE.LineBasicMaterial({
      color: 0x00ff00,
      transparent: true,
      opacity: 0.5,
    })

    // Buat mesh dengan EdgesGeometry untuk efek wireframe
    const box = new THREE.LineSegments(
      new THREE.EdgesGeometry(geometry),
      material
    )

    // Posisikan box sesuai dengan area ring
    box.position.z = RING_AREA.minZ + RING_AREA.depth / 2

    return box
  }

  const handleSuspendOrDeletedUser = () => {
    console.log('handle suspend or deleted user')
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    setUserLocalStorage('allValue', '')
    setUser(null)
    if (
      ['congratulation', 'level-up', 'result', 'missed'].includes(
        gameState.current
      )
    ) {
      setTimeout(() => {
        setGameStatus('idle')
        gameState.current = 'idle'
      }, 6000)
    } else {
      setGameStatus('idle')
      gameState.current = 'idle'
    }
  }

  useEffect(() => {
    const getUser = async () => {
      try {
        const token = localStorage.getItem('token')

        if (!token) {
          return null
        }

        const response = await fetch(API_GET_USER, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        const data = await response.json()

        if (data.status === 200) {
          const userData = data.data
          userData.allow_bonus_level = userData.latest_usage_bonus_level
            ? new Date().getTime() -
                new Date(userData.latest_usage_bonus_level).getTime() >
              2 * 24 * 60 * 60 * 1000
            : true
          // Simpan data user di localStorage
          // console.log('sync user')
          localStorage.setItem('user', JSON.stringify(userData))
          setUser(userData)
          redirectToRegion(userData.region, country)
          if (!user?.oreo_cookie) generateCookiePosition()
          return userData
        }

        if (
          ['User has suspended', 'User not found db'].includes(data.message)
        ) {
          handleSuspendOrDeletedUser()
        }

        return null
      } catch (error) {
        console.error('Error fetching user data:', error)
        return null
      }
    }
    getUser()
  }, [])

  const gotoHome = () => {
    setGameStatus('idle')
    gameState.current = 'idle'
    showOreo()
    clearRings()
  }

  const gotoPage = (page: string) => {
    clearRings()
    setGameStatus(page)
    gameState.current = page
    hideOreo()
    targetIntensity.current = 0
  }

  const LoadingScreen = () => (
    <div className='fixed inset-0 bg-black z-50 flex flex-col items-center justify-center'>
      <div role='status'>
        <svg
          aria-hidden='true'
          className='w-16 h-16 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600'
          viewBox='0 0 100 101'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z'
            fill='currentColor'
          />
          <path
            d='M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z'
            fill='currentFill'
          />
        </svg>
        <span className='sr-only'>Loading...</span>
      </div>
    </div>
  )
  const returnToPlay = () => {
    if (user && getUserLocalStorage('status') !== 'firstTimePlaying') {
      resetLevelAndGenerateCookiePosition()
      gameState.current = 'aiming'
      setGameStatus('aiming')
      targetIntensity.current = 1
      createRings()
    } else {
      setUserLocalStorage('allValue', '')
      gameState.current = 'login'
      setGameStatus('login')
    }
  }

  useEffect(() => {
    countryRef.current = country
  }, [country])

  const signOut = () => {
    localStorage.setItem('lastPlayGame', 'false')
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
    setUser(null)
    gotoHome()
  }

  return (
    <>
      <Analytic/>
      <Dekstop />
      <div className='sm:hidden block relative w-screen h-full   bg-black'>
        {isLoading && <LoadingScreen />}
        <div
          ref={containerRef}
          style={{width: '100%', height: '100%', position: 'relative'}}
          // className='mb-10'
        >
          {gameStatus === 'idle' && (
            <Landing
              gameStatus={gameStatus}
              setGameStatus={setGameStatus}
              user={user}
              setUser={setUser}
              firstTimePlaying={firstTimePlaying}
              gotoPage={gotoPage}
              gotoHome={gotoHome}
              returnToPlay={returnToPlay}
              signOut={signOut}
            />
          )}
        </div>
        {/* Backdrop */}
        {['leaderboard'].includes(gameStatus) && (
          <div className='fade-in fixed w-full h-full top-0 left-0 bg-[#00227A] bg-opacity-50 z-10'></div>
        )}
        <div className='fixed w-full h-full z-10 top-0 left-0 flex flex-col'>
          <PopupOreoStar
          trigger={showPopup}
          onClose={() => setShowPopup(false)}
        />
          {gameStatus !== 'idle' && (
            <Header

          goToPage={gotoPage}
          gameStatus={gameStatus}
          signOut={signOut}
        />)}

          {RenderTitle}
          {RenderDesc}
          {RenderTimer}
          {['congratulation', 'level-up'].includes(gameStatus) && (
            <div className='getOreoName w-full flex absolute bottom-0 items-center justify-evenly  gap-4 fade-in'>
              <Title text={getOreoName()} className='fade-in uppercase' />
            </div>
          )}
          {gameStatus === 'result' && user && (
            <div className='font-medium flex flex-col items-center px-4 mb-4 gap-2 mt-2 absolute w-full top-[40%] left-1/2 -translate-x-[50%]  -translate-y-[50%]'>
              <div className='grid grid-cols-2 w-full max-w-[400px] gap-0'>
                <Card
                className='col-span-2 border-b-0'
                content={(
                  <>
                    <p className='text-xs'>
                      {translate('yourTotalPoints', country as Language)}
                    </p>
                    <p className='text-[50px]'>{user?.point ?? 0}</p>
                  </>
                )}
              />
                <Card
                className='p-1 border-r-0'
                content={(
                  <>
                    <p className='text-[10px]'>
                      {translate('previousPoints', country as Language)}
                    </p>
                    <p className='text-[19px]'>{displayedPoints.previous}</p>
                  </>
                )}
              />
                <Card
                className='p-1'
                content={(
                  <>
                    {showTextBonusLevel ? (
                      <p className='text-[10px]'>
                        {translate('bonusPoints', country as Language)}
                      </p>
                    ) : (
                      <p className='text-[10px]'>
                        {translateLevelUpPoint(
                            country as Language,
                            user?.level ?? 0
                        )}
                      </p>
                    )}
                    <p className='text-[19px]'>+{displayedPoints.reward}</p>
                  </>
                )}
              />
              </div>
              <Card
              className='py-[16px] gap-2'
              content={(
                <>
                  <Title text={translate(
                    'collect5OreoSpaceEmbossment',
                        country as Language
                  )} className='text-xs' />
                  <CatchOreo user={user} displayOn='result' />

                </>
              )}
            />
            </div>
          )}

          {gameStatus === 'leaderboard' && (
            <>
              <div className='fade-in font-medium flex flex-col items-center px-4 gap-2 mt-12 absolute w-full top-[40%] left-1/2 -translate-x-[50%]  -translate-y-[50%]'>
                <Leaderboard />
              </div>
              <div className='fade-in w-full flex flex-col absolute bottom-0 items-center justify-center mb-20 gap-2 px-4'>
                <Button
                id={generatePlayLevelId(user?.status ?? '', user?.level ?? 0)}
                onClick={() => {
                  playAgain()
                  PlayLevel(user?.status ?? '', user?.level ?? 0)
                }}
                label={translate('playAgain', country as Language)}
              />
                <Timer
                duration={30}
                className=''
                onTimeUp={playAgain}
                variant='autoplay'
              />
              </div>
            </>
          )}

          {gameStatus === 'tutorial' && (

            <Youtube close={closeTutorial} />

          )}
          {/* content */}
          {gameStatus === 'aiming' && user && (
            <>
              <div className='w-full flex absolute bottom-0 items-center justify-evenly mb-24 gap-4 fade-in'>
                <ScanButton
                onClick={() => {
                  setGameStatus('bonus-level')
                  gameState.current = 'bonus-level'
                  targetIntensity.current = 0
                  hideOreo()
                }}
                disabled={!user?.allow_bonus_level}
              />
                <DunkNowBtn
                id={generateDunkNowId(user?.status ?? '', user?.level ?? 0)}
                onClick={() => {
                  if (!oreo.current) return
                  allowOreoMovement.current = false
                  setGameStatus('shooting')
                  gameState.current = 'shooting'
                  allowOreoMovement.current = true
                  DunkNow(user?.status ?? '', user?.level ?? 0)
                }}
              />
                <CatchOreo user={user} displayOn='game' />

              </div>
            </>
          )}
          {['shooting', 'congratulation', 'level-up'].includes(gameStatus) && (
            <>

              <div className='w-full flex absolute bottom-0 items-center justify-center mb-24 gap-4'>
                <Joystick onMove={onMove} onEnd={onEnd} />
              </div>
            </>
          )}
          {gameStatus === 'time-out' && (
            <>
              <div className='w-full flex flex-col absolute bottom-0 items-center justify-center mb-20 gap-4 px-4'>
                <Button
                id='MissedPlayAgain'
                onClick={() => {
                  playAgain()
                  MissedPlayAgain()
                }}
                label='PLAY AGAIN' />
                <Button
                id='NewPlaySkip'
                onClick={() => {
                  goToResult()
                  NewPlaySkip()
                }}
                label='SKIP & GO TO RESULT'
              />
              </div>
            </>
          )}
          {gameStatus === 'missed' && (
            <>
              <div className='w-full flex flex-col absolute bottom-0 items-center justify-center mb-20 gap-4 px-4'>
                <Button
                id='MissedPlayAgain'
                onClick={() => {
                  isPlayBonusLevel ? playAgainBonusLevel() : playAgain()
                  MissedPlayAgain()
                }}
                 label={translate('playAgain', country as Language)}
              />
                <Button
                id='NewPlaySkip'
                onClick={() => {
                  isPlayBonusLevel ? goToResultBonusLevel() : goToResult()
                  NewPlaySkip()
                }}
                label={translate('skipAndGoToResult', country as Language)}
              />
              </div>
            </>
          )}

          {gameStatus === 'result' && (
            <>
              <div className='w-full flex flex-col absolute bottom-0 items-center justify-center mb-20 gap-2 px-4'>
                <Button
                id={generatePlayBonusId(user?.status ?? '', user?.level ?? 0)}
                className='z-[60]'
                onClick={() => {
                  if (getUserLocalStorage('status') === 'firstTimePlaying') {
                    setGameStatus('action-need')
                    gameState.current = 'action-need'
                  }
                  setGameStatus('bonus-level')
                  gameState.current = 'bonus-level'
                  PlayBonus(user?.status ?? '', user?.level ?? 0)
                }}
                label={
                    user?.allow_bonus_level
                      ? translate(
                        'continueToPlayBonusLevels',
                          country as Language
                      )
                      : translate('bonusLevelOpenIn2Days', country as Language)
                  }
                 disabled={!user?.allow_bonus_level}
                variant={user?.allow_bonus_level ? 'blue' : 'translucent'}
              />
                <Button
                id={generatePlayLevelId(user?.status ?? '', user?.level ?? 0)}
                onClick={
                    getUserLocalStorage('status') === 'firstTimePlaying'
                      ? () => gotoPage('action-need')
                      : () => {
                        playAgain()
                        PlayLevel(user?.status ?? '', user?.level ?? 0)
                      }
                  }
                label={translate('playWithoutScan', country as Language)}
              />
                <Button
                id={generateSubmitPointId(user?.status ?? '', user?.level ?? 0)}
                onClick={() => {
                  goToLeaderboard()
                  SubmitPoint(showTextBonusLevel ? 'playBonusLevel' : user?.status ?? '', user?.level ?? 0)
                }}
                 label={translate(
                   'submitPointAndViewLeaderboard',
                    country as Language
                 )}
              />
              </div>
            </>
          )}

          {gameStatus === 'action-need' && (
            <>
              <div className='fade-in  flex flex-col items-center px-4 gap-2 mt-[58px] absolute w-full'>
                <ActionNeed onRegis={handleRegis} goToLogin={goToLogin} />

              </div>
            </>
          )}
          {gameStatus === 'cookie-policy' && (
            <CookiePolicy gotoHome={gotoHome} />
          )}
          {gameStatus === 'update-login-pin' && (
            <div className='fade-in  flex flex-col items-center px-4 gap-2 mt-10 absolute w-full top-[40%] left-1/2 -translate-x-[50%]  -translate-y-[50%]'>
              <UpdateLoginPin
              type={type}
              email={email}
              handleUpdateLoginPin={handleUpdateLoginPin}
              gotoPage={gotoPage}
            />
            </div>
          )}

          {gameStatus === 'login' && (
            <div className='fade-in flex flex-col items-center px-4 gap-2 mt-10 absolute w-full top-[40%] left-1/2 -translate-x-[50%] -translate-y-[50%]'>
              <Login onLogin={handleLogin} onForgotPassword={handleForgotPassword} />
            </div>
          )}

          {gameStatus === 'forgot-password' && (
            <div className='fade-in flex flex-col items-center px-4 gap-2 mt-10 absolute w-full top-[40%] left-1/2 -translate-x-[50%] -translate-y-[50%]'>
              <ForgotPassword
              onBackToLogin={handleBackToLogin}
              onForgotSuccess={handleForgotSuccess}
            />
            </div>
          )}
          {gameStatus === 'bonus-level' && (
            <div className='fade-in flex flex-col items-center px-4 gap-2 mt-10 absolute w-full top-[40%] left-1/2 -translate-x-[50%] -translate-y-[50%]'>
              <BonusLevel setGameStatus={setGameStatus} playAgain={playAgain} />
            </div>
          )}
          {gameStatus === 'buy-product' && (
            <div className='fade-in flex flex-col items-center px-4 gap-2 mt-10 absolute w-full top-[40%] left-1/2 -translate-x-[50%] -translate-y-[50%]'>
              <BuyProduct setGameStatus={setGameStatus} />
            </div>
          )}

          {gameStatus === 'scan-cookie' && (
          // <div className="fade-in flex flex-col items-center px-4 gap-2 mt-10 absolute w-full top-[40%] left-1/2 -translate-x-[50%] -translate-y-[50%]">
            <ScanCookie
            gotoPage={gotoPage}
            signOut={signOut}
            playBonusLevel={playBonusLevel}
            handleBack={() => {
              setGameStatus('bonus-level')
              gameState.current = 'bonus-level'
              // console.log('handleBack')
            }}
          />
          )}

          {gameStatus === 'oreo-space-dunk' && <OreoSpaceDunk gotoHome={gotoHome} />}
          {gameStatus === 'the-prizes' && <ThePrizes gotoHome={gotoHome} />}
          {gameStatus === 'how-to-participate' && <HowToParticipate gotoHome={gotoHome} />}

          {gameStatus !== 'idle' && <Footer goToPage={gotoPage} />}

          {/* <div className="flex flex-col gap-4">
          <Button
            onClick={() => {}}
            label="CONTINUE TO PLAY BONUS LEVELS"
            variant="blue"
          />
          <Button onClick={() => {}} label="Click me" />
        </div> */}
        </div>

        {showButtonDebug && (
          <div className='fixed top-4 right-4 flex gap-2 z-50 w-[80vw] overflow-x-auto'>
            <button
            className='bg-blue-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={() => setShowDebug(!showDebug)}
          >
              {showDebug ? 'Hide Debug' : 'Show Debug'}
            </button>
            <button
            className='bg-blue-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={() => createRings()}
          >
              Reset Ring
            </button>
            <button
            className='bg-green-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={showOreo}
          >
              Fade In
            </button>
            <button
            className='bg-red-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={hideOreo}
          >
              Fade Out
            </button>
            <button
            className='bg-purple-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={scaleUp}
          >
              Scale Up
            </button>
            <button
            className='bg-yellow-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={scaleDown}
          >
              Scale Down
            </button>
            <button
            className='bg-indigo-500 text-white px-3 py-1 rounded-lg shadow-lg text-sm'
            onClick={() => {
              const currentLevel = getUserLocalStorage('level') || 1
              const nextLevel = (currentLevel % 4) + 1
              setUserLocalStorage('level', nextLevel)
            }}
          >
              Change Logo
            </button>
          </div>
        )}

        {showDebug && (
          <div
           className='fixed top-16 right-4 bg-white p-4 rounded-lg shadow-lg z-50 h-[50vh] overflow-y-auto'
          style={{maxWidth: '250px'}}
        >
            <h3 className='text-black font-bold mb-2'>Debug Controls</h3>
            <div className='space-y-2'>
              <div>
                <label className='text-black text-sm'>Size</label>
                <div className='flex gap-2'>
                  <input
                  type='range'
                  min='0.1'
                  max='3'
                  step='0.1'
                  value={debugSize}
                  onChange={e => handleSizeChange(parseFloat(e.target.value))}
                  className='flex-1'
                />
                  <input
                  type='number'
                  min='0.1'
                  max='3'
                  step='0.1'
                  value={debugSize}
                  onChange={e => handleSizeChange(parseFloat(e.target.value))}
                  className='w-16 text-black text-sm p-1 border rounded'
                />
                </div>
              </div>
              <div>
                <label className='text-black text-sm'>X Rotation</label>
                <div className='flex gap-2'>
                  <input
                  type='range'
                  min='-180'
                  max='180'
                  step='0.1'
                  value={debugRotation.x}
                  onChange={e => handleRotationChange('x', parseFloat(e.target.value))
                  }
                  className='flex-1'
                />
                  <input
                  type='number'
                  min='-180'
                  max='180'
                  step='0.1'
                  value={debugRotation.x}
                  onChange={e => handleRotationChange('x', parseFloat(e.target.value))
                  }
                  className='w-16 text-black text-sm p-1 border rounded'
                />
                </div>
              </div>
              <div>
                <label className='text-black text-sm'>Y Rotation</label>
                <div className='flex gap-2'>
                  <input
                  type='range'
                  min='-180'
                  max='180'
                  step='0.1'
                  value={debugRotation.y}
                  onChange={e => handleRotationChange('y', parseFloat(e.target.value))
                  }
                  className='flex-1'
                />
                  <input
                  type='number'
                  min='-180'
                  max='180'
                  step='0.1'
                  value={debugRotation.y}
                  onChange={e => handleRotationChange('y', parseFloat(e.target.value))
                  }
                  className='w-16 text-black text-sm p-1 border rounded'
                />
                </div>
              </div>
              <div>
                <label className='text-black text-sm'>Z Rotation</label>
                <div className='flex gap-2'>
                  <input
                  type='range'
                  min='-180'
                  max='180'
                  step='0.1'
                  value={debugRotation.z}
                  onChange={e => handleRotationChange('z', parseFloat(e.target.value))
                  }
                  className='flex-1'
                />
                  <input
                  type='number'
                  min='-180'
                  max='180'
                  step='0.1'
                  value={debugRotation.z}
                  onChange={e => handleRotationChange('z', parseFloat(e.target.value))
                  }
                  className='w-16 text-black text-sm p-1 border rounded'
                />
                </div>
              </div>
              <button
              className='bg-red-500 text-white px-2 py-1 rounded text-sm w-full'
              onClick={() => setDebugRotation({x: 0, y: 0, z: 0})}
            >
                Reset
              </button>
              <div>
                <label className='text-black text-sm'>Opacity</label>
                <div className='flex gap-2'>
                  <input
                  type='range'
                  min='0'
                  max='1'
                  step='0.01'
                  value={oreoOpacity.current}
                  onChange={e => fadeOreo(parseFloat(e.target.value), 0)}
                  className='flex-1'
                />
                  <input
                  type='number'
                  min='0'
                  max='1'
                  step='0.01'
                  value={oreoOpacity.current}
                  onChange={e => fadeOreo(parseFloat(e.target.value), 0)}
                  className='w-16 text-black text-sm p-1 border rounded'
                />
                </div>
              </div>
              <div>
                <label className='text-black text-sm'>Scale Animation</label>
                <div className='flex gap-2'>
                  <input
                  type='range'
                  min='0'
                  max='3'
                  step='0.01'
                  value={oreoScale.current}
                  onChange={e => scaleOreo(parseFloat(e.target.value), 0)}
                  className='flex-1'
                />
                  <input
                  type='number'
                  min='0'
                  max='3'
                  step='0.01'
                  value={oreoScale.current}
                  onChange={e => scaleOreo(parseFloat(e.target.value), 0)}
                  className='w-16 text-black text-sm p-1 border rounded'
                />
                </div>
              </div>

              <div className='mt-4'>
                <h4 className='text-black font-bold mb-2'>Logo Controls</h4>

                {/* Position Controls */}
                <div className='mb-4'>
                  <h5 className='text-black text-sm font-semibold mb-2'>
                    Position
                  </h5>
                  {['x', 'y', 'z'].map(axis => (
                    <div
                    key={`pos-${axis}`}
                    className='flex items-center gap-2 mb-1'
                  >
                      <label className='text-black text-sm w-8'>
                        {axis.toUpperCase()}
                      </label>
                      <input
                      type='range'
                      min='-2'
                      max='2'
                      step='0.01'
                      value={
                        debugLogo.position[
                          axis as keyof typeof debugLogo.position
                        ]
                      }
                      onChange={(e) => {
                        setDebugLogo(prev => ({
                          ...prev,
                          position: {
                            ...prev.position,
                            [axis]: parseFloat(e.target.value),
                          },
                        }))
                      }}
                      className='flex-1'
                    />
                      <input
                      type='number'
                      min='-2'
                      max='2'
                      step='0.01'
                      value={
                        debugLogo.position[
                          axis as keyof typeof debugLogo.position
                        ]
                      }
                      onChange={(e) => {
                        setDebugLogo(prev => ({
                          ...prev,
                          position: {
                            ...prev.position,
                            [axis]: parseFloat(e.target.value),
                          },
                        }))
                      }}
                      className='w-16 text-black text-sm p-1 border rounded'
                    />
                    </div>
                  ))}
                </div>

                {/* Scale Controls */}
                <div className='mb-4'>
                  <h5 className='text-black text-sm font-semibold mb-2'>Scale</h5>
                  {['x', 'y', 'z'].map(axis => (
                    <div
                    key={`scale-${axis}`}
                    className='flex items-center gap-2 mb-1'
                  >
                      <label className='text-black text-sm w-8'>
                        {axis.toUpperCase()}
                      </label>
                      <input
                      type='range'
                      min='0'
                      max='1'
                      step='0.01'
                      value={
                        debugLogo.scale[axis as keyof typeof debugLogo.scale]
                      }
                      onChange={(e) => {
                        setDebugLogo(prev => ({
                          ...prev,
                          scale: {
                            ...prev.scale,
                            [axis]: parseFloat(e.target.value),
                          },
                        }))
                      }}
                      className='flex-1'
                    />
                      <input
                      type='number'
                      min='0'
                      max='1'
                      step='0.01'
                      value={
                        debugLogo.scale[axis as keyof typeof debugLogo.scale]
                      }
                      onChange={(e) => {
                        setDebugLogo(prev => ({
                          ...prev,
                          scale: {
                            ...prev.scale,
                            [axis]: parseFloat(e.target.value),
                          },
                        }))
                      }}
                      className='w-16 text-black text-sm p-1 border rounded'
                    />
                    </div>
                  ))}
                </div>

                {/* Rotation Controls */}
                <div className='mb-4'>
                  <h5 className='text-black text-sm font-semibold mb-2'>
                    Rotation
                  </h5>
                  {['x', 'y', 'z'].map(axis => (
                    <div
                    key={`rot-${axis}`}
                    className='flex items-center gap-2 mb-1'
                  >
                      <label className='text-black text-sm w-8'>
                        {axis.toUpperCase()}
                      </label>
                      <input
                      type='range'
                      min='-6.28'
                      max='6.28'
                      step='0.01'
                      value={
                        debugLogo.rotation[
                          axis as keyof typeof debugLogo.rotation
                        ]
                      }
                      onChange={(e) => {
                        setDebugLogo(prev => ({
                          ...prev,
                          rotation: {
                            ...prev.rotation,
                            [axis]: parseFloat(e.target.value),
                          },
                        }))
                      }}
                      className='flex-1'
                    />
                      <input
                      type='number'
                      min='-6.28'
                      max='6.28'
                      step='0.01'
                      value={
                        debugLogo.rotation[
                          axis as keyof typeof debugLogo.rotation
                        ]
                      }
                      onChange={(e) => {
                        setDebugLogo(prev => ({
                          ...prev,
                          rotation: {
                            ...prev.rotation,
                            [axis]: parseFloat(e.target.value),
                          },
                        }))
                      }}
                      className='w-16 text-black text-sm p-1 border rounded'
                    />
                    </div>
                  ))}
                </div>

                {/* Reset Button */}
                <button
                className='bg-red-500 text-white px-2 py-1 rounded text-sm w-full'
                onClick={() => {
                  setDebugLogo({
                    position: {x: 0, y: 0, z: 0},
                    scale: {x: 1, y: 1, z: 1},
                    rotation: {x: Math.PI, y: Math.PI, z: Math.PI},
                  })
                }}
              >
                  Reset Logo Transform
                </button>
              </div>

              <div className='flex items-center gap-2'>
                <label className='text-black text-sm'>Show Ring Area</label>
                <input
                type='checkbox'
                checked={showRingArea}
                onChange={e => setShowRingArea(e.target.checked)}
                className='form-checkbox h-4 w-4 text-blue-600'
              />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}) /* Rectangle 1 */

/* Rectangle 1 */

export {Home}

// Login
// Aiming
// Shooting
// Congratulation/Failed
// Result
// Leaderboard
