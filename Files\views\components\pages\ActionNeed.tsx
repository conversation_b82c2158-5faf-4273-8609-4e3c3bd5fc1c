declare let React: any

declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
const {useState, useRef} = React
import Card from '../atoms/Card'
import Button from '../atoms/Button'
import CustomDropdown from '../atoms/CustomDropdown'
import DOBDropdown from '../atoms/DOBDropdown'
import Title from '../atoms/Title'
import {
  API_GENERATE_OTP,
} from '../../config/apiConfig'
import Alert from '../atoms/Alert'
import {APP_MODE} from '../../config/appConfig'
import {Language, translate} from '../../config/translation'

import {
  getAllCountryCodes,
  getCurrentCountry,
} from '../../config/countryCode'
import getPrizeByRegion, {PrizeType} from '../../config/getPrizeByRegion'
// const scanOreoStar = require('../../../assets/images/scan-oreo-star.png')
import {NewRegister} from '../../analytics/events'

type Props = {
  onRegis: (email: string) => void;
  goToLogin: () => void;

};

const ActionNeed = ({onRegis, goToLogin}: Props) => {
  const {country} = useParams()
  const alltimePrize = getPrizeByRegion(country, 'alltime') as PrizeType
  // Find the country code based on URL parameter
  const currentCountry = getCurrentCountry(country)
  const initialCountryCode = currentCountry?.code || '+62'

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    countryCode: initialCountryCode,
    phone: '',
    email: '',
    dateOfBirth: '',
    acceptTnC: false,
    acceptPrivacy: false,
    acceptMarketing: false,
  })

  const [showDatePlaceholder, setShowDatePlaceholder] = useState(true)
  const [errorMessages, setErrorMessages] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    dateOfBirth: '',
    general: '',
  })

  // Define state variables for the selected date
  const [selectedDay, setSelectedDay] = useState('')
  const [selectedMonth, setSelectedMonth] = useState('')
  const [selectedYear, setSelectedYear] = useState('')

  const handleSelect = (day: string, month: string, year: string) => {
    setSelectedDay(day)
    setSelectedMonth(month)
    setSelectedYear(year)

    // Only update dateOfBirth if we have all three values
    if (day && month && year) {
      // Format as MM/DD/YYYY to match your existing format
      const formattedDate = `${month.padStart(2, '0')}/${day.padStart(2, '0')}/${year}`

      setFormData(prev => ({
        ...prev,
        dateOfBirth: formattedDate,
      }))

      // Clear any dateOfBirth error message
      setErrorMessages(prev => ({
        ...prev,
        dateOfBirth: '',
      }))
    }
  }

  const [isLoading, setIsLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')

  const validatePhoneNumber = (phone: string, countryCode: string) => {
    const selectedCountry = getAllCountryCodes().find(c => c.code === countryCode)
    if (!selectedCountry) return false

    // Hapus semua karakter non-digit
    const cleanPhone = phone.replace(/\D/g, '')

    return (
      cleanPhone.length >= selectedCountry.minLength &&
      cleanPhone.length <= selectedCountry.maxLength
    )
  }

  const formatPhoneNumber = (phone: string, countryCode: string) => {
    const cleanPhone = phone.replace(/\D/g, '')
    let formattedPhone = cleanPhone

    switch (countryCode) {
      case '+62':  // Indonesia
        formattedPhone = cleanPhone.replace(
          /(\d{4})(\d{4})(\d{4})/,
          '$1 $2 $3'
        )
        break
      case '+63':  // Philippines
        formattedPhone = cleanPhone.replace(
          /(\d{4})(\d{3})(\d{4})/,
          '$1 $2 $3'
        )
        break
      case '+66':  // Thailand
        formattedPhone = cleanPhone.replace(
          /(\d{3})(\d{3})(\d{3})/,
          '$1 $2 $3'
        )
        break
      case '+65':  // Singapore
        formattedPhone = cleanPhone.replace(/(\d{4})(\d{4})/, '$1 $2')
        break
      case '+84':  // Vietnam
        formattedPhone = cleanPhone.replace(
          /(\d{3})(\d{3})(\d{4})/,
          '$1 $2 $3'
        )
        break
      case '+855':  // Cambodia
        formattedPhone = cleanPhone.replace(
          /(\d{3})(\d{3})(\d{3})/,
          '$1 $2 $3'
        )
        break
      case '+60':  // Malaysia
        formattedPhone = cleanPhone.replace(
          /(\d{3})(\d{3})(\d{4})/,
          '$1-$2 $3'
        )
        break
      case '+679':  // Fiji
        formattedPhone = cleanPhone.replace(/(\d{3})(\d{4})/, '$1 $2')
        break
      case '+852':  // Hong Kong
        formattedPhone = cleanPhone.replace(/(\d{4})(\d{4})/, '$1 $2')
        break
      case '+886':  // Taiwan
        formattedPhone = cleanPhone.replace(
          /(\d{4})(\d{3})(\d{3})/,
          '$1 $2 $3'
        )
        break
    }
    return formattedPhone
  }
  const handleChange = (e) => {
    const {name, value, type} = e.target
    const {checked} = e.target as HTMLInputElement
    if (name === 'phone') {
      // Hanya izinkan angka dan spasi
      const cleanValue = value.replace(/[^\d\s-]/g, '')
      // Batasi panjang input sesuai maxLength negara yang dipilih
      const selectedCountry = getAllCountryCodes().find(
        c => c.code === formData.countryCode
      )
      const maxDigits = selectedCountry ? selectedCountry.maxLength : 12
      const truncatedValue = cleanValue.replace(/\D/g, '').slice(0, maxDigits)

      const formattedValue = formatPhoneNumber(
        truncatedValue,
        formData.countryCode
      )

      setFormData(prev => ({
        ...prev,
        [name]: formattedValue,
      }))

      // Validasi nomor telepon
      if (!validatePhoneNumber(truncatedValue, formData.countryCode)) {
        const country = getAllCountryCodes().find(
          c => c.code === formData.countryCode
        )
        let errorMsg = 'Invalid phone number'
        if (country) {
          if (country.minLength === country.maxLength) {
            errorMsg += ` (${country.minLength} digits)`
          } else {
            errorMsg += ` (${country.minLength}-${country.maxLength} digits)`
          }
        }
        setErrorMessages(prev => ({
          ...prev,
          phone: errorMsg,
        }))
      } else {
        setErrorMessages(prev => ({
          ...prev,
          phone: '',
        }))
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value,
      }))
    }

    if (name === 'dateOfBirth') {
      let inputValue = value.replace(/\D/g, '')
      const prevValue = formData.dateOfBirth.replace(/\D/g, '')

      // Handle backspace by comparing lengths
      if (inputValue.length < prevValue.length) {
        // If deleting a separator, also remove the number before it
        if (value.length === 3 || value.length === 6) {
          inputValue = inputValue.slice(0, -1)
        }
      }

      // Build the formatted date
      let formattedDate = ''

      // Handle each part of the date (MM/DD/YYYY)
      if (inputValue.length > 0) {
        // Add month (first 2 digits)
        formattedDate = inputValue.slice(0, 2)

        // Add day (next 2 digits) with separator
        if (inputValue.length >= 2) {
          if (inputValue.length > 2) {
            formattedDate += `/${inputValue.slice(2, 4)}`
          } else {
            formattedDate += '/'
          }

          // Add year (last 4 digits) with separator
          if (inputValue.length >= 4) {
            if (inputValue.length > 4) {
              formattedDate += `/${inputValue.slice(4, 8)}`
            } else {
              formattedDate += '/'
            }
          }
        }
      }

      // Update form data with formatted date
      setFormData(prev => ({
        ...prev,
        [name]: formattedDate,
      }))

      // Validate complete dates
      if (formattedDate.split('/').join('').length === 8) {
        const [month, day, year] = formattedDate.split('/')
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
        const isValid = date.getMonth() === parseInt(month) - 1 &&
                       date.getDate() === parseInt(day) &&
                       date.getFullYear() === parseInt(year)

        // setErrorMessages(prev => ({
        //   ...prev,
        //   dateOfBirth: isValid ? '' : 'Please enter a valid date'
        // }))
      }

      return
    }

    if (name !== 'phone') {
      setErrorMessages(prev => ({
        ...prev,
        [name]: '',
      }))
    }
  }

  const handleCountryCodeSelect = (code: string) => {
    setFormData(prev => ({
      ...prev,
      countryCode: code,
    }))
  }

  const handleSubmit = async () => {
    NewRegister()
    let isValid = true
    const newErrorMessages = {
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      dateOfBirth: '',
      general: '',
    }

    // Validasi form
    if (!formData.firstName) {
      newErrorMessages.firstName = translate('actionNeed_FirstName_Required', country as Language)
      isValid = false
    }
    if (!formData.lastName) {
      newErrorMessages.lastName = translate('actionNeed_LastName_Required', country as Language)
      isValid = false
    }
    if (!formData.phone) {
      newErrorMessages.phone = translate('actionNeed_Phone_Required', country as Language)
      isValid = false
    }
    if (!formData.email) {
      newErrorMessages.email = translate('actionNeed_Email_Required', country as Language)
      isValid = false
    }
    if (!formData.dateOfBirth) {
      newErrorMessages.dateOfBirth = translate(
        'actionNeed_DateOfBirth_Required',
        country as Language
      )
      isValid = false
    }

    if (!isValid) {
      setErrorMessages(newErrorMessages)
      return
    }

    // Clear error messages
    setErrorMessages({
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      dateOfBirth: '',
      general: '',
    })

    setIsLoading(true)

    try {
      if (APP_MODE === 'prod') {
        // Fetch generate OTP setelah registrasi berhasil
        const otpResponse = await fetch(API_GENERATE_OTP, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: formData.email,
            type: 'register',
            region: country?.toLocaleLowerCase(),
          }),  // Payload email
        })

        const otpData = await otpResponse.json()

        if (!otpResponse.ok) {
          throw new Error(otpData.message || 'Failed to generate OTP')
        }
        onRegis(formData.email)
      } else if (APP_MODE === 'test') {
        onRegis(formData.email)
      }
    } catch (error) {
      const errorMessage =
        error.message || 'An unknown error occurred'
      setAlertMessage(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Tambahkan fungsi untuk mengecek apakah form valid
  const isFormValid = () => (
    formData.firstName !== '' &&
      formData.lastName !== '' &&
      formData.phone !== '' &&
      formData.email !== '' &&
      formData.dateOfBirth !== '' &&
      formData.acceptTnC &&
      formData.acceptPrivacy
  )

  const formContent = (
    <form className='space-y-2 font-normal w-full'>
      <div>
        <input
          type='text'
          name='firstName'
           placeholder={translate('enterFirstName', country as Language)}
          value={formData.firstName}
          onChange={handleChange}
          maxLength={
              formData.countryCode
                ? (getAllCountryCodes().find(c => c.code === formData.countryCode)
                  ?.maxLength || 12) + 4
                : 16
            }
          className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
        />
        {errorMessages.firstName && (
          <p className='text-red-500 text-[10px] mt-1 ml-1'>
            {errorMessages.firstName}
          </p>
        )}
      </div>
      <div>
        <input
          type='text'
          name='lastName'
          placeholder={translate('enterLastName', country as Language)}
          value={formData.lastName}
          onChange={handleChange}
          className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
        />
        {errorMessages.lastName && (
          <p className='text-red-500 text-[10px] mt-1 ml-1'>
            {errorMessages.lastName}
          </p>
        )}
      </div>

      <div className='flex gap-2'>
        <CustomDropdown
          options={getAllCountryCodes()}
          selectedOption={formData.countryCode}
          onSelect={handleCountryCodeSelect}
          disabled={true}
        />
        <div className='flex-1'>
          <input
            type='tel'
            name='phone'
            placeholder={translate('enterPhone', country as Language)}
            value={formData.phone}
            onChange={handleChange}
            className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
          />
          {errorMessages.phone && (
            <p className='text-red-500 text-[10px] mt-1 ml-1'>
              {errorMessages.phone}
            </p>
          )}
        </div>
      </div>

      <div>
        <input
          type='email'
          name='email'
          placeholder={translate('enterEmail', country as Language)}
          value={formData.email}
          onChange={handleChange}
          className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
        />
        {errorMessages.email && (
          <p className='text-red-500 text-[10px] mt-1 ml-1'>
            {errorMessages.email}
          </p>
        )}
      </div>

      <div className='relative'>
        <div className='flex items-center p-2 border-b border-white'>
          <label className='text-white text-[12px] '>{translate('birthDate', country as Language)}*</label>
          <div className='flex-1'>
            <DOBDropdown
              selectedDOB={{day: selectedDay, month: selectedMonth, year: selectedYear}}
              onSelectDOB={dob => handleSelect(dob.day, dob.month, dob.year)}
            />
          </div>
        </div>
        {errorMessages.dateOfBirth && (
          <p className='text-red-500 text-[10px] mt-1 ml-1'>
            {errorMessages.dateOfBirth}
          </p>
        )}
      </div>

      {errorMessages.general && (
        <p className='text-red-500 text-[10px] mt-1 ml-1'>
          {errorMessages.general}
        </p>
      )}
    </form>
  )

  return (
    <div className='flex flex-col gap-4 overflow-y-auto h-[calc(100svh-58px-76px)] py-4 px-4 '>
      <div className='flex flex-col items-center gap-2'>
        <img
           src={alltimePrize.prize}
          alt='Prize All Time'
          className='w-full max-w-[200px] fade-in'
        />
        <Title text={translate('actionNeeded', country as Language)} className='fade-in uppercase' />
        <p className='fade-in text-center text-[12px] mt-1 ml-1 '>
          {translate('completeForm', country as Language)}
        </p>
      </div>
      <Card content={formContent} />

      <div className='space-y-4'>
        <label className='flex items-center gap-2 text-white'>
          <input
            type='checkbox'
            name='acceptTnC'
            checked={formData.acceptTnC}
            onChange={handleChange}
            className='w-[20px] h-[20px]'
          />
          <span className='text-[12px] max-w-[300px]'>
            {translate('beforeTermsAndConditions', country as Language)}
            <a
            href={currentCountry?.termAndCondition}
            target='_blank'
            className='text-sm underline text-[12px]'
          >
              {translate('termsAndConditions', country as Language)}*
            </a>
          </span>
        </label>
        <label className='flex items-center gap-2 text-white'>
          <input
            type='checkbox'
            name='acceptPrivacy'
            checked={formData.acceptPrivacy}
            onChange={handleChange}
            className='w-[20px] h-[20px]'
          />
          <span className='text-[12px] max-w-[300px]'>
            {translate('beforePrivacyPolicy', country as Language)}
            <a
            href={currentCountry?.privacyPolicy}
            target='_blank'
            className='text-sm underline text-[12px]'
          >
              {translate('privacyPolicy', country as Language)}*
            </a>
          </span>
        </label>
        <label className='flex items-start gap-2 text-white'>
          <input
            type='checkbox'
            name='acceptMarketing'
            checked={formData.acceptMarketing}
            onChange={handleChange}
            className='w-[20px] h-[20px] flex-shrink-0'
          />
          <span className='underline text-[12px] max-w-[300px]'>
            {translate('acceptMarketing', country as Language)}*
          </span>
        </label>
      </div>

      <div className='mt-8'>
        {alertMessage && <Alert message={alertMessage} />}

        <Button id='NewRegister'
        className='uppercase'
          label={translate('register', country as Language)}
         onClick={handleSubmit} isLoading={isLoading} disableHoverEffect={true} disabled={!isFormValid()} />
        <p
          onClick={goToLogin}
          className='text-sm text-center underline cursor-pointer mt-8'

        >
          {translate('alreadyHaveAccount', country as Language)}
        </p>
      </div>

    </div>
  )
}

export default ActionNeed
