declare let React: any
const {useState} = React

import Card from '../atoms/Card'
import Button from '../atoms/Button'
import Alert from '../atoms/Alert'
import Title from '../atoms/Title'
import {API_GENERATE_OTP} from '../../config/apiConfig'
import {APP_MODE} from '../../config/appConfig'
import {ForgotSubmit} from '../../analytics/events'
import {translate} from '../../config/translation'
import {Language} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

const ForgotPassword = ({
  onBackToLogin,
  onForgotSuccess,
}: {
onBackToLogin: () => void;
onForgotSuccess: (email: string) => void;
}) => {
  const {country} = useParams()
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')
  const [errorMessage, setErrorMessage] = useState('')

  const handleEmailChange = (e) => {
    setEmail(e.target.value)
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setAlertMessage('')
    setErrorMessage('')

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!email) {
      setErrorMessage(translate('emailRequired', country as Language))
      setIsLoading(false)
      return
    } else if (!emailPattern.test(email)) {
      setErrorMessage(translate('invalidEmailFormat', country as Language))
      setIsLoading(false)
      return
    }

    try {
      if (APP_MODE === 'prod') {
        const response = await fetch(API_GENERATE_OTP, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            type: 'forgotPassword',
            region: country?.toLocaleLowerCase(),
          }),
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || 'Failed to send reset email')
        }

        onForgotSuccess(email)
      } else if (APP_MODE === 'test') {
        onForgotSuccess('<EMAIL>')
      }
    } catch (error) {
      const errorMessage =
        (error as { response?: { data?: { message?: string } } }).response?.data
          ?.message ||
        (error as Error).message ||
        'Internal server error'
      setAlertMessage(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className='flex flex-col gap-4'>
      <Title text={translate('forgotLoginPin', country as Language)} className='fade-in' />
      <p className='fade-in text-center text-xs mb-8'>
        {translate('forgotPinInstructions', country as Language)}
      </p>
      <Card
        className='mb-8'
        content={(
          <div className='w-full'>
            <input
              type='text'
              placeholder={translate('enterEmailAddress', country as Language)}
              value={email}
              onChange={handleEmailChange}
              className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
            />
            {errorMessage && (
              <p className='text-red-500 text-[10px] mt-1 ml-1'>
                {errorMessage}
              </p>
            )}
          </div>
        )}
      />
      <Button
      id='ForgotSubmit'
      onClick={() => {
        handleSubmit()
        ForgotSubmit()
      }}
      label={translate('submit', country as Language)}
      isLoading={isLoading}
      disableHoverEffect={true} />
      <Button id='back-to-login' onClick={onBackToLogin} label={translate('backToLogin', country as Language)} />
      {alertMessage && <Alert message={alertMessage} />}
    </div>
  )
}

export default ForgotPassword
