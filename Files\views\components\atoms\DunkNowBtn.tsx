declare let React: any
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
const {useState, useEffect} = React
import {Language, translate} from '../../config/translation'
interface IDunkNowBtnProps {
  onClick: () => void;
  id?: string;
}

const HandIcon = () => (
  <svg
    width='41'
    height='54'
    viewBox='0 0 41 54'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className='animate-fade-in-out'
  >
    <path
      d='M9.19545 32.9023V12.1816C9.19545 10.1213 10.7262 8.19151 12.789 8.19151C14.8568 8.19151 16.7465 9.8302 16.7465 11.8905V22.6335M16.7465 22.6335L16.7264 25.5847M16.7465 22.6335C18.1593 17.321 24.6588 19.3587 24.2598 23.5269C24.2523 23.6022 24.2598 25.3789 24.2598 25.3789M24.2598 25.3789V28.2397M24.2598 25.3789C24.97 20.5858 32.8472 20.4528 31.8434 27.7629M31.8434 27.7629L31.7857 29.4894M31.8434 27.7629C33.0229 22.7188 39.4597 23.8055 39.289 28.4555V37.7079C39.2815 41.6629 38.5111 43.555 36.4659 45.9365C36.0644 46.4032 35.6754 46.8876 35.3818 47.4296C34.3077 49.4196 34.7996 49.9642 34.6214 52.0721M9.19796 23.0175C5.88546 26.0138 3.65704 28.7491 2.98952 29.5672C0.756093 32.9575 1.32323 35.1809 4.49521 39.7055C6.85913 43.0682 9.02982 45.4873 9.19545 45.678C10.8843 47.5852 10.7338 48.8475 10.7338 52.0972'
      stroke='white'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M23.0604 11.9407C23.0604 6.39473 18.5558 1.90278 12.9974 1.90278C10.3323 1.90011 7.77521 2.9561 5.88858 4.83849C4.00194 6.72089 2.94022 9.27556 2.93689 11.9407'
      stroke='white'
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
)

const DunkNowBtn = ({onClick, id}: IDunkNowBtnProps) => {
  const {country} = useParams()

  const [showHand, setShowHand] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowHand(true)
    }, 10000)

    return () => clearTimeout(timer)
  }, [])

  const t = translate('dunkNow', country as Language).split(' ')

  return (
    <button
      id={id}
      onClick={onClick}
      className='uppercase font-bold flex flex-col items-center justify-center w-[100px] h-[100px] border border-white rounded-full backdrop-blur-[7.5px]'
    >
      {showHand ? (
        <HandIcon />
      ) : (
        <>
          <span>{t[0]}</span>
          <span>{t[1]}</span>
        </>
      )}
    </button>
  )
}

export default DunkNowBtn
