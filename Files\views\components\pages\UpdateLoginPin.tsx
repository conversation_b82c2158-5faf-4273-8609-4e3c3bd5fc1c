declare let React: any
const {useState, useRef, useEffect} = React
import Card from '../atoms/Card'
import Button from '../atoms/Button'
import Title from '../atoms/Title'
import {API_VERIFY_OTP} from '../../config/apiConfig'
import Alert from '../atoms/Alert'
import {UserData} from '../../types/UserData'
import {APP_MODE} from '../../config/appConfig'
import {ForgotSubmitOTP, NewRegisSubmit} from '../../analytics/events'
import {
  Language,
  translate,
  translateOtpSent,
  translateResendOtp,
} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

type Props = {
    type: string;
  email: string;
  handleUpdateLoginPin: (userData?: UserData) => void;
  gotoPage: (page: string) => void;
};

const Timer = ({duration}: { duration: number }) => {
  const [timeLeft, setTimeLeft] = useState(duration * 60)  // Konversi menit ke detik

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prevTimeLeft => prevTimeLeft - 1)
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [timeLeft])

  const minutes = Math.floor(timeLeft / 60)
  const seconds = timeLeft % 60

  return (
    <div className={'  flex justify-center items-center'}>
      <div className='text-center'>
        <span className='text-[10px] text-white font-medium'>
          {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
        </span>
      </div>
    </div>
  )
}

const UpdateLoginPin = ({type, email, handleUpdateLoginPin, gotoPage}: Props) => {
  const {country} = useParams()
  const [otp, setOtp] = useState('')
  const [password, setPassword] = useState(['', '', '', ''])
  const [isLoading, setIsLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')
  const [errorMessages, setErrorMessages] = useState({
    otp: '',
    pin: '',
  })

  const passwordRefs = useRef([])

  const handleOtpChange = (e) => {
    setOtp(e.target.value)
  }

  const handlePasswordChange = (index: number, value: string) => {
    const newPassword = [...password]
    newPassword[index] = value
    setPassword(newPassword)

    if (value && index < password.length - 1) {
      passwordRefs.current[index + 1]?.focus()
    }

    if (!value && index > 0) {
      passwordRefs.current[index - 1]?.focus()
    }
  }

  const handleKeyDown = (
    e: any,
    index: number
  ) => {
    // Menangani backspace
    if (e.key === 'Backspace' && !password[index] && index > 0) {
      passwordRefs.current[index - 1]?.focus()
    }

    // Menangani tombol panah kanan
    if (e.key === 'ArrowRight' && index < password.length - 1) {
      passwordRefs.current[index + 1]?.focus()
    }

    // Menangani tombol panah kiri
    if (e.key === 'ArrowLeft' && index > 0) {
      passwordRefs.current[index - 1]?.focus()
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setErrorMessages({otp: '', pin: ''})  // Reset error messages

    // Validasi OTP dan PIN
    let isValid = true

    if (!otp) {
      setErrorMessages(prev => ({...prev, otp: 'OTP is required.'}))
      isValid = false
    }

    if (password.some(digit => digit === '')) {
      setErrorMessages(prev => ({
        ...prev,
        pin: 'All PIN digits are required.',
      }))
      isValid = false
    }

    if (!isValid) {
      setIsLoading(false)
      return
    }

    // Menyiapkan payload
    const payload = {
      email,
      code: otp,
      type,  // Ganti dengan "forgotPassword" atau "login" jika diperlukan
      password: password.join(''),  // Menggabungkan password dari array menjadi string
    }

    try {
      if (APP_MODE === 'prod') {
        const response = await fetch(API_VERIFY_OTP, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || 'Failed to verify OTP')
        }
        if (type === 'register') {
          const userData = data.data.user
          userData.oreo_cookie = [0, 1, 2, 3, 4]
          userData.allow_bonus_level = userData.latest_usage_bonus_level
            ? new Date().getTime() -
              new Date(userData.latest_usage_bonus_level).getTime() >
            2 * 24 * 60 * 60 * 1000
            : true
          console.log('OTP verification successful:', data)
          localStorage.setItem('token', data.data.token)
          localStorage.setItem('refreshToken', data.data.refreshToken)
          localStorage.setItem('user', JSON.stringify(userData))
          handleUpdateLoginPin(userData)
        } else {
          handleUpdateLoginPin()
        }
      } else if (APP_MODE === 'test') {
        handleUpdateLoginPin({
          id: 11,
          hubspot_id: '86186912014',
          first_name: 'lail',
          last_name: 'ka',
          username: 'lailka0919',
          email: '<EMAIL>',
          phone: '09192811',
          point: 0,
          level: null,
          latest_usage_bonus_level: null,
          is_active: true,
          created_at: '2024-12-19T16:56:52.199Z',
          updated_at: '2024-12-19T16:56:52.199Z',
          allow_bonus_level: false,
          is_success_bonus: false,
          region: 'sg',
          oreo_cookie: [0, 1, 2, 3, 4],
        })
      }
    } catch (error) {
      const errorMessage =
        (error as { response?: { data?: { message?: string } } }).response?.data
          ?.message ||
        error.message ||
        'Internal server error'
      setAlertMessage(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const renderPasswordInputs = () => password.map((digit, index) => (
    <input
        key={index}
        type='password'
        maxLength={1}
        value={digit}
        onChange={e => handlePasswordChange(index, e.target.value)}
        onKeyDown={e => handleKeyDown(e, index)}
        ref={el => (passwordRefs.current[index] = el)}
        className='w-10 h-10 text-center border border-gray-300 rounded text-[28px]'
        inputMode='numeric'
        pattern='[0-9]*'
      />
  ))

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex flex-col items-center gap-2 mb-4'>
        <Title text={translate('updateLoginPin', country as Language)} className='fade-in uppercase' />
        <p className='fade-in text-center text-[10px] max-w-[300px] mt-1 ml-1'>
          {translateOtpSent(country as Language, email)}
        </p>
      </div>
      <Card
        content={(
          <div className='w-full'>
            <input
              type='text'
               placeholder={translate('insertOtpHere', country as Language)}
              value={otp}
              onChange={handleOtpChange}
              className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
            />
            <button
              //  onClick={handleResendOtp}
              className='text-white text-[10px] flex gap-1 mt-1'
            >
              {translateResendOtp(country as Language, <Timer duration={10} />)}
            </button>
            {errorMessages.otp && otp === '' && (
              <p className='text-red-500 text-[10px] mt-1  '>
                {errorMessages.otp}
              </p>
            )}
          </div>
        )}
      />
      <Card
        content={(
          <div className='flex flex-col gap-2 items-center'>
            <p className='text-xs'>{translate('createLoginPassword', country as Language)}</p>
            <div className='flex gap-2 text-black'>
              {renderPasswordInputs()}
            </div>
            {errorMessages.pin && password.some(digit => digit === '') && (
              <p className='text-red-500 text-[10px] mt-1 ml-1'>
                {errorMessages.pin}
              </p>
            )}
          </div>
        )}
      />
      <div className='mt-8 flex flex-col gap-4'>
        <Button
          id={type === 'register' ? 'NewRegisSubmit' : 'ForgotSubmitOTP'}
          onClick={() => {
            handleSubmit()
            if (type === 'register') {
              NewRegisSubmit()
            } else {
              ForgotSubmitOTP()
            }
          }}
          label={translate('submit', country as Language)}
          isLoading={isLoading}
          disableHoverEffect={true}
        />
        <Button
          onClick={() => gotoPage('forgot-password')}
          label={translate('backToForm', country as Language)}
          disabled={isLoading}
          disableHoverEffect={true}
        />
      </div>

      {alertMessage && <Alert message={alertMessage} />}
    </div>
  )
}

export default UpdateLoginPin
