declare let React: any
const {useState, useRef, useEffect} = React
import { Language } from '../../config/translation';
import { translate } from '../../config/translation';
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface Option {
  code: string
  label: string
}

interface CustomDropdownProps {
  options: Option[]
  selectedOption: string
  onSelect: (code: string) => void
  disabled?: boolean
}

const CustomDropdown = ({options, selectedOption, onSelect, disabled = false}: CustomDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)

  const toggleDropdown = () => {
    if (disabled) return
    setIsOpen((prev) => !prev)
  }

  const handleOptionClick = (code: string) => {
    if (disabled) return
    onSelect(code)
    setIsOpen(false)
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className='relative' ref={dropdownRef}>
      <div
        onClick={toggleDropdown}
        className={`bg-transparent text-white ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} text-[12px] flex justify-between items-center`}
      >
        <span className={['Day', 'Month', 'Year'].includes(selectedOption) ? 'text-white/50' : 'text-white'}>
          {selectedOption}
        </span>
        {!disabled && <span className='ml-2'>&#9662;</span>}
      </div>
      {isOpen && !disabled && (
        <div
          style={{
            backdropFilter: 'blur(9px)',
            backgroundColor: 'rgba(0, 23, 84, 0.9)',
          }}
          className='absolute z-10 bg-transparent border border-[#1D46B0] mt-1 min-w-max max-h-[200px] overflow-y-scroll'>
          {options.filter(option => option.code !== '').map((option) => (
            <div
              key={option.code}
              onClick={() => handleOptionClick(option.code)}
              className='p-2 text-white text-[12px] hover:bg-white hover:text-[#1D46B0] cursor-pointer border-b border-[#1D46B0] last:border-b-0'
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

const getDaysInMonth = (month: number, year: number) => {
  return new Date(year, month, 0).getDate()
}

const DOBDropdown = ({selectedDOB, onSelectDOB}) => {
  const { country } = useParams();
  const currentYear = new Date().getFullYear()
  const minYear = currentYear - 17
  const maxYear = 1900

  // Calculate days in month based on selected month and year
  const daysInMonth = selectedDOB.month ? getDaysInMonth(Number(selectedDOB.month), Number(selectedDOB.year) || minYear) : 31

  // Generate day options
  const days = Array.from({length: daysInMonth}, (_, i) => (i + 1).toString())

  const months = [
    {code: '1', label: 'January'},
    {code: '2', label: 'February'},
    {code: '3', label: 'March'},
    {code: '4', label: 'April'},
    {code: '5', label: 'May'},
    {code: '6', label: 'June'},
    {code: '7', label: 'July'},
    {code: '8', label: 'August'},
    {code: '9', label: 'September'},
    {code: '10', label: 'October'},
    {code: '11', label: 'November'},
    {code: '12', label: 'December'},
  ]

  const years = Array.from({length: minYear - maxYear + 1}, (_, i) => (minYear - i).toString())

  // This effect adjusts the day when month or year changes make the current day invalid
  useEffect(() => {
    if (selectedDOB.day && selectedDOB.month && selectedDOB.year) {
      const maxDays = getDaysInMonth(Number(selectedDOB.month), Number(selectedDOB.year))

      // If current day is greater than max days in new month, adjust it
      if (Number(selectedDOB.day) > maxDays) {
        onSelectDOB({
          ...selectedDOB,
          day: maxDays.toString()
        })
      }
    }
  }, [selectedDOB.month, selectedDOB.year])

  const handleSelect = (type: string, value: string) => {
    onSelectDOB({...selectedDOB, [type]: value})
  }

  return (
    <div className='flex gap-3 justify-self-end'>
      <CustomDropdown
        options={days.map((d) => ({ code: d, label: d }))}
        selectedOption={selectedDOB.day || translate('day', country as Language)}
        onSelect={(value) => handleSelect('day', value)}
      />
      <CustomDropdown
        options={months}
        selectedOption={months.find((m) => m.code === selectedDOB.month)?.label || translate('month', country as Language)}
        onSelect={(value) => handleSelect('month', value)}
      />
      <CustomDropdown
        options={years.map((y) => ({ code: y, label: y }))}
        selectedOption={selectedDOB.year || translate('year', country as Language)}
        onSelect={(value) => handleSelect('year', value)}
      />
    </div>
  )
}

export default DOBDropdown
