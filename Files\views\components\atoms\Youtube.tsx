import Button from './Button'
declare let React: any
import { translate, Language } from '../../config/translation';
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM


interface YoutubeProps {
  close: () => void;
}
export default function Youtube({close}: YoutubeProps) {
  const { country } = useParams();

  return (
    <div className='w-full h-full flex flex-col items-center justify-center   px-12 pt-4 pb-24 gap-4'>
      <div className=' w-fit h-[50vh] bg-black rounded-xl relative overflow-hidden '>
        <iframe
          src='https://www.youtube-nocookie.com/embed/NKERb3jueLA'
          title='YouTube video player'
          allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
          allowFullScreen
          className='w-full h-full aspect-[9/16]'
        ></iframe>
      </div>
      <Button
        label={translate('skipTutorial', country as Language)}
        onClick={close}
        className='!max-w-[300px] w-full'
      />
    </div>
  )
}
