declare let React: any

interface Props {
  text: string;
  curvature: number;
  position: 'above' | 'below';
}

export default function CurvedText({
  text = '',
  curvature = 140,
  position = 'below',
}: Props) {
  //   const [text, setText] = useState('Oreo Shooting Star');
  //   const [curvature, setCurvature] = useState(140);
  //   const [position, setPosition] = useState('below'); // "above" or "below"

  // Calculate the path for the text to follow
  const calculateTextPath = () => {
    // For a circular arc
    const centerX = 150
    const centerY = 100
    const radius = curvature

    // Fixed arc angle of 180 degrees
    const startAngle = 180
    const endAngle = 0

    // Direction based on position
    const direction = position === 'above' ? -1 : 1

    const startX = centerX + radius * Math.cos((startAngle * Math.PI) / 180)
    const startY =
      centerY + radius * Math.sin((startAngle * Math.PI) / 180) * direction
    const endX = centerX + radius * Math.cos((endAngle * Math.PI) / 180)
    const endY =
      centerY + radius * Math.sin((endAngle * Math.PI) / 180) * direction

    // Create the arc path
    const largeArcFlag = 0  // 180 degrees is not a large arc
    const sweepFlag = position === 'above' ? 0 : 1

    return `M ${startX},${startY} A ${radius},${radius} 0 ${largeArcFlag},${sweepFlag} ${endX},${endY}`
  }

  return (
    <div style={{maxWidth: '500px', margin: '0 auto'}}>
      <div
        style={{
          //   border: '1px solid #ddd',
          borderRadius: '8px',
          marginBottom: '20px',
          //   background: 'white',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <svg width='300' height='470' viewBox='0 0 300 300'>
          <defs>
            <path
              id='textPath'
              d={calculateTextPath()}
              fill='none'
              stroke='#ddd'
              strokeWidth='1'
            />
          </defs>
          {/* <use href="#textPath" /> */}
          <text>
            <textPath
              href='#textPath'
              startOffset='50%'
              textAnchor='middle'
              fontSize='36'
              fontWeight={800}
              fill='white'
            >
              {text}
            </textPath>
          </text>
        </svg>
      </div>

      {/* <div style={{ marginBottom: '15px' }}>
        <label
          style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}
        >
          Text
        </label>
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #ccc',
            borderRadius: '4px',
          }}
        />
      </div>

      <div style={{ marginBottom: '15px' }}>
        <label
          style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}
        >
          Curvature: {curvature}
        </label>
        <input
          type="range"
          min="50"
          max="300"
          value={curvature}
          onChange={(e) => setCurvature(Number.parseInt(e.target.value))}
          style={{ width: '100%' }}
        />
      </div>

      <div>
        <label
          style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}
        >
          Text Position
        </label>
        <div style={{ display: 'flex', gap: '15px' }}>
          <label style={{ display: 'flex', alignItems: 'center' }}>
            <input
              type="radio"
              name="position"
              value="above"
              checked={position === 'above'}
              onChange={() => setPosition('above')}
              style={{ marginRight: '5px' }}
            />
            Above
          </label>
          <label style={{ display: 'flex', alignItems: 'center' }}>
            <input
              type="radio"
              name="position"
              value="below"
              checked={position === 'below'}
              onChange={() => setPosition('below')}
              style={{ marginRight: '5px' }}
            />
            Below
          </label>
        </div>
      </div> */}
    </div>
  )
}
