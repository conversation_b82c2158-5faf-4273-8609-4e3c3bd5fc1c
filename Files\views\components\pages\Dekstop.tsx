declare let React: any

const spaceBgDekstop = require('../../../assets/images/space-bg-dekstop.jpg')
const oreoLogo = require('../../../assets/images/oreo-logo.png')
const mockup = require('../../../assets/images/mockup.png')
import {Language} from '../../config/translation'
import {translate} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
const qr = require('../../../assets/images/qr.jpg')

const Dekstop = () => {
  const {country} = useParams()
  return (
    <div className='sm:block absolute z-[-10] h-full w-screen'>
      <div className='absolute z-10 top-0 left-0 w-full h-full flex items-center justify-center gap-16'>
        <img src={mockup} alt='mockup' className='w-[211px]' />
        <div className='flex flex-col items-center justify-center gap-8'>
          <img src={oreoLogo} alt='oreo-packaging' className='w-[216px]' />
          <p className='text-white text-center  '>
            {translate(
              'theOreoSpaceExperienceIsOnlyAccessibleThroughMobile',
            country as Language
            )}{' '}
            <br />
            {translate(
              'pleaseSwitchYourDeviceToGetTheBestExperience',
            country as Language
            )}
          </p>
          <img src={qr} alt='qr' className='w-[225px]' />

          <p className='text-white text-center text-2xl  '>{translate('scanHere', country as Language)}</p>
        </div>
      </div>
      <img
        src={spaceBgDekstop}
        alt='space-bg-dekstop'
        className='w-full h-full object-cover'
      />
    </div>
  )
}
export default Dekstop
