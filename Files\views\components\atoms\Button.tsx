declare let React: any
const {ReactNode} = React

interface IButtonProps {
  onClick?: (e?: any) => void // Use 'any' to avoid React.MouseEvent typing
  label: typeof ReactNode
  variant?: 'transparent' | 'blue' | 'white' | 'translucent'
  isLoading?: boolean
  className?: string
  disableHoverEffect?: boolean
  disabled?: boolean
  id?: string
}

const Button = ({
  onClick,
  label,
  variant = 'transparent',
  isLoading = false,
  className = '',
  disableHoverEffect = false,
  disabled = false,
  id,
}: IButtonProps) => (
  <button
    onClick={(e) => {
      if (!disabled && !isLoading && onClick) onClick(e)
    }}
    disabled={disabled || isLoading}
    id={id}
    className={`w-full relative max-w-[400px] ${
      !disableHoverEffect ? 'group' : ''
    } ${className} ${disabled ? 'opacity-70 cursor-not-allowed' : ''}`}
  >
    <>
      {variant === 'transparent' && (
        <svg
          width='100%'
          height='41'
          viewBox='0 0 300 41'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
          preserveAspectRatio='none'
        >
          <path
            d='M0.5 1.25006L290.299 1.25L299.5 10.4911V40.25H11.8892L0.5 29.1904V1.25006Z'
            fill='url(#paint0_linear_733_777)'
            fillOpacity='0.1'
            stroke='white'
            className='transition-all'
          />
          <path
            d='M0 0.804749L290.507 0.804688L300 10.3394V40.8047H11.6863L0 29.4565V0.804749Z'
            fill='white'
            className='opacity-0 group-hover:opacity-100 transition-opacity'
          />
          <defs>
            <linearGradient
              id='paint0_linear_733_777'
              x1='150'
              y1='0.75'
              x2='150'
              y2='40.75'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='#07163E' />
              <stop offset='1' stopColor='#1D46B0' />
            </linearGradient>
          </defs>
        </svg>
      )}

      {variant === 'blue' && (
        <svg
          width='100%'
          height='41'
          viewBox='0 0 300 41'
          fill='none'
          preserveAspectRatio='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M0.5 1.25006L290.299 1.25L299.5 10.4911V40.25H11.8892L0.5 29.1904V1.25006Z'
            fill='url(#paint0_linear_733_774)'
            stroke='white'
          />
          <defs>
            <linearGradient
              id='paint0_linear_733_774'
              x1='0'
              y1='20.75'
              x2='300'
              y2='20.75'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='#00227A' />
              <stop offset='1' stopColor='#003EE0' />
            </linearGradient>
          </defs>
        </svg>
      )}

      {variant === 'white' && (
        <svg
          width='100%'
          height='41'
          viewBox='0 0 300 41'
          fill='none'
          preserveAspectRatio='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M0 0.804749L290.507 0.804688L300 10.3394V40.8047H11.6863L0 29.4565V0.804749Z'
            fill='white'
          />
        </svg>
      )}

      {variant === 'translucent' && (
        <svg
          width='100%'
          height='41'
          viewBox='0 0 300 41'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
          preserveAspectRatio='none'
        >
          <path
            d='M0 0.804749L290.507 0.804688L300 10.3394V40.8047H11.6863L0 29.4565V0.804749Z'
            fill='#D6D6D640'
          />
        </svg>
      )}

      <span className='absolute w-full left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%]'>
        {isLoading ? (
          <span className='flex items-center justify-center'>
            <svg
              version='1.1'
              id='L9'
              xmlns='http://www.w3.org/2000/svg'
              x='0px'
              y='0px'
              viewBox='0 0 100 100'
              enableBackground='new 0 0 0 0'
              className='w-10 h-10'
            >
              <path
                fill='#fff'
                d='M73,50c0-12.7-10.3-23-23-23S27,37.3,27,50 M30.9,50c0-10.5,8.5-19.1,19.1-19.1S69.1,39.5,69.1,50'
              >
                <animateTransform
                  attributeName='transform'
                  attributeType='XML'
                  type='rotate'
                  dur='1s'
                  from='0 50 50'
                  to='360 50 50'
                  repeatCount='indefinite'
                />
              </path>
            </svg>
            <span>Loading...</span>
          </span>
        ) : (
          <span
            className={`uppercase w-full flex items-center justify-center absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 ${
              variant === 'transparent'
                ? 'text-white group-hover:text-[#00227A] transition-colors'
                : ''
            }`}
          >
            {label}
          </span>
        )}
      </span>
    </>
  </button>
)

export default Button
