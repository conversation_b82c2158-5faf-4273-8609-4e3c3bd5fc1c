declare let React: any
const mondelezLogo = require('../../../assets/mondelez-logo.png')
const earth = require('../../../assets/earth.png')
import {Language, translate} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
import {getCurrentCountry} from '../../config/countryCode'

interface FooterProps {
  className?: string;
  disableOverlay?:boolean;
  goToPage: (page: string) => void;
}
const Footer = ({className, disableOverlay = false, goToPage}:FooterProps) => {
  const {country} = useParams()
  const countryData = getCurrentCountry(country)

  return (
    <div
    className={`${className} ${!disableOverlay && ''} text-[#00227A] absolute left-1/2 translate-x-[-50%] w-full text-center bottom-0 bg-no-repeat bg-center bg-cover`}
    style={{backgroundImage: `url(${earth})`}}
  >
      <div className='bg-gradient-to-t from-white from-20% to-transparent pt-4 pb-3'>
        <p className='text-[9px] font-medium'>
          <a href={countryData?.termAndCondition} target='_blank'>{translate('termsAndConditions', country as Language)}</a> •
          <a href={countryData?.privacyPolicy} target='_blank'> {translate('privacyPolicy', country as Language)}</a> •
          <span
            onClick={() => {
              goToPage('cookie-policy')
            }}
          >
            {translate('cookiePolicy', country as Language)}
          </span>
          <a href={countryData?.contactUs} target='_blank'> {translate('contactUs', country as Language)}</a>
        </p>
        <p className='text-[9px]'>
          © {translate('copyright', country as Language)}
        </p>
        <img
          src={mondelezLogo}
          className='w-[71px] pt-1 mx-auto'
          alt='Mondelez Logo'
        />
      </div>
    </div>
  )
}

export default Footer
