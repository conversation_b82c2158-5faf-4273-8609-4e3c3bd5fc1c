declare let React: any
const {useRef, useState} = React
import Button from '../atoms/Button'
import Title from '../atoms/Title'
import {HamburgerPlay} from '../../analytics/events'
import getPrizeByRegion, {PrizeType} from '../../config/getPrizeByRegion'
declare let ReactRouterDOM: any

import {translate} from '../../config/translation'
import {Language} from '../../config/translation'
const {useParams} = ReactRouterDOM

interface ThePrizesProps {
  gotoHome: () => void;
}

const ThePrizes = ({gotoHome}: ThePrizesProps) => {
  const {country} = useParams()
  const countryCode = country || 'id'

  const alltimePrize = getPrizeByRegion(countryCode, 'alltime') as PrizeType
  const weeklyPrize = getPrizeByRegion(countryCode, 'biweekly') as PrizeType
  const isOnlyAlltime = weeklyPrize.prize === ''

  const prizes = [
    {
      id: 1,
      title: translate('prize_grandPrize', country as Language),
      description: alltimePrize?.description || '',
      image: alltimePrize?.prize || '',
    },
    {
      id: 2,
      title: translate('prize_biWeeklyPrize', country as Language),
      description: weeklyPrize?.description || '',
      image: weeklyPrize?.prize || '',
    },
  ]

  if (isOnlyAlltime) {
    prizes.splice(1, 1)
  }
  const ref = useRef(null)
  const [activePrize, setActivePrize] = useState(0)

  const handleClick = (direction: string) => {
    if (ref.current) {
      const newIndex =
        direction === 'right' ? activePrize + 1 : activePrize - 1
      setActivePrize(newIndex)

      ref.current.scrollTo({
        left: ref.current.offsetWidth * newIndex,
        behavior: 'smooth',
      })
    }
  }

  return (
    <div className='w-full h-full flex flex-col items-center justify-center pb-12 fade-in'>
      <Title text={translate('prize_title', country as Language)} className='mb-2' />
      <p className='text-white text-center text-[12px] max-w-[175px]'>
        {translate('prize_desc', country as Language)}
      </p>

      <div ref={ref} className='scrollbar-hide overflow-scroll w-full mt-4'>
        <div className='flex'>
          {prizes.map(prize => (
            <div
              key={prize.id}
              className='w-full flex-shrink-0 flex flex-col items-center justify-center gap-2'
            >
              <img
                src={prize.image}
                alt={prize.title}
                className='w-full max-w-[300px] mb-2'
              />
              <Title text={prize.title} className='text-white text-center' />
              <p className='text-white text-center text-[12px]  max-w-[250px]'>
                {prize.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      <div className='relative w-full max-w-[800px] mt-4'>
        <div className='flex flex-col gap-4 px-4'>
          {!isOnlyAlltime && <div className='grid grid-cols-2 gap-4'>
            <Button
              variant='transparent'
              disableHoverEffect
              disabled={activePrize === 0}
              label={(
                <svg
                  width='30'
                  height='18'
                  viewBox='0 0 30 18'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M8.55135 16.1414L1.41003 9.00004L8.55135 1.85873M2.40188 9.00004L28.5899 9.00004'
                    stroke='white'
                    strokeWidth='1.5'
                    strokeMiterlimit='10'
                    strokeLinecap='square'
                  />
                </svg>
              )}
              onClick={() => handleClick('left')}
              className=''
            />
            <Button
              variant='transparent'
              disableHoverEffect
              disabled={activePrize === prizes.length - 1}
              label={(
                <svg
                  width='30'
                  height='18'
                  viewBox='0 0 30 18'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M21.4487 1.85876L28.59 9.00008L21.4487 16.1414M27.5981 9.00008L1.41008 9.00007'
                    stroke='white'
                    strokeWidth='1.5'
                    strokeMiterlimit='10'
                    strokeLinecap='square'
                  />
                </svg>
              )}
              onClick={() => handleClick('right')}
              className=''
            />
          </div>}
          <Button
            id='HamburgerPlay'
            variant='transparent'
            label={translate('playNow', country as Language)}
            onClick={() => {
              gotoHome()
              HamburgerPlay()
            }}
            className='w-full'
          />
        </div>
      </div>
    </div>
  )
}

export default ThePrizes
