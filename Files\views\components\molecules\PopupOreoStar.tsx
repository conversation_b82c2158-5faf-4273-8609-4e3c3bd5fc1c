declare let React: any
const {useEffect, useState} = React

import Card from '../atoms/Card'
const scanOreoStar = require('../../../assets/images/scan-oreo-star.png')
import {Language} from '../../config/translation'
import {translate} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface PopupOreoStarProps {
  onClose: () => void;
  trigger?: boolean;
}

const PopupOreoStar = ({
  onClose,
  trigger = false,
}:PopupOreoStarProps) => {
  const {country} = useParams()
  const [opacity, setOpacity] = useState(0)
  const [shouldRender, setShouldRender] = useState(false)

  useEffect(() => {
    if (trigger) {
      setShouldRender(true)
      setTimeout(() => setOpacity(1), 50)

      const autoCloseTimer = setTimeout(() => {
        handleClose()
      }, 5000)

      return () => clearTimeout(autoCloseTimer)
    } else {
      setOpacity(0)
      setTimeout(() => setShouldRender(false), 300)
    }
  }, [trigger])

  const handleClose = () => {
    setOpacity(0)
    setTimeout(() => {
      setShouldRender(false)
      onClose()
    }, 300)
  }

  if (!shouldRender) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className='fixed inset-0 z-[45] bg-black bg-opacity-80'
        style={{
          opacity,
          transition: 'opacity 0.3s ease-in-out',
          pointerEvents: opacity === 0 ? 'none' : 'auto',
        }}
        onClick={handleClose}
      ></div>
      {/* Popup Content */}
      <div
        className='fixed inset-0 z-[55] flex items-center justify-center px-8 pointer-events-none popup-oreo-star'
        style={{
          opacity,
          transition: 'opacity 0.3s ease-in-out',
        }}
      >
        <Card
          className='relative w-full max-w-md pointer-events-auto'
          content={(
            <div className='flex flex-col items-center p-4 font-medium'>
              <button onClick={handleClose} className='absolute top-4 right-4'>
                <svg
                  width='20'
                  height='21'
                  viewBox='0 0 20 21'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M3.05288 17.6914C2.09778 16.7689 1.33596 15.6655 0.811868 14.4455C0.287778 13.2254 0.0119157 11.9132 0.000377568 10.5854C-0.0111606 9.25764 0.241856 7.94084 0.744665 6.71188C1.24747 5.48291 1.99001 4.36639 2.92893 3.42747C3.86786 2.48854 4.98438 1.74601 6.21334 1.2432C7.44231 0.740392 8.7591 0.487375 10.0869 0.498913C11.4147 0.510451 12.7269 0.786313 13.9469 1.3104C15.167 1.83449 16.2704 2.59632 17.1929 3.55142C19.0145 5.43744 20.0224 7.96346 19.9996 10.5854C19.9768 13.2074 18.9251 15.7155 17.0711 17.5696C15.217 19.4237 12.7089 20.4754 10.0869 20.4982C7.46493 20.5209 4.9389 19.513 3.05288 17.6914ZM4.46288 16.2814C5.96401 17.7825 7.99997 18.6259 10.1229 18.6259C12.2458 18.6259 14.2818 17.7825 15.7829 16.2814C17.284 14.7803 18.1273 12.7443 18.1273 10.6214C18.1273 8.49851 17.284 6.46254 15.7829 4.96142C14.2818 3.46029 12.2458 2.61697 10.1229 2.61697C7.99997 2.61697 5.96401 3.46029 4.46288 4.96142C2.96176 6.46254 2.11843 8.49851 2.11843 10.6214C2.11843 12.7443 2.96176 14.7803 4.46288 16.2814ZM14.3629 7.79142L11.5329 10.6214L14.3629 13.4514L12.9529 14.8614L10.1229 12.0314L7.29288 14.8614L5.88288 13.4514L8.71288 10.6214L5.88288 7.79142L7.29288 6.38142L10.1229 9.21142L12.9529 6.38142L14.3629 7.79142Z'
                    fill='white'
                  />
                </svg>
              </button>

              <h2 className='text-base mb-4 flex flex-col items-center justify-center'>
                <span>{translate('wellDone', country as Language)}</span>
                <span>{translate('readyForMoreFun', country as Language)}</span>
              </h2>

              <div className='relative w-[140px] h-[140px] mb-6'>
                <img
                  src={scanOreoStar}
                  alt='Scan Oreo Star'
                  className='w-full h-full object-contain'
                />
              </div>

              <p className='text-center text-xs'>
                {translate(
                  'submitYourPointsAndDiveIntoBonusLevels',
                  country as Language
                )}
              </p>
            </div>
          )}
        />
      </div>
    </>
  )
}

export default PopupOreoStar
