declare let window: any

export const triggerConversion = (sendTo: string, params: Record<string, any> = {}) => {
  const {gtag} = window

  // Grab the focused button (the one that was clicked)
  const activeElement = document.activeElement as HTMLElement | null
  const buttonId = activeElement?.id || 'unknown'

  if (typeof gtag === 'function') {
    gtag('event', 'conversion', {
      allow_custom_scripts: true,
      send_to: sendTo,
      click_id: buttonId,
      ...params,
    })
    console.log(`Conversion sent to ${sendTo}`, {buttonId, ...params})
  } else {
    console.warn('gtag is not defined')
  }
}

export const triggerConversionTTQ = (event:string) => {
  const {ttq} = window

  // Grab the focused button (the one that was clicked)
  const activeElement = document.activeElement as HTMLElement | null
  const buttonId = activeElement?.id || 'unknown'
  if (typeof ttq.track === 'function') {
    ttq.track(event)
    console.log(`Conversion ttq for event ${event}`, {buttonId})
  } else {
    console.warn('ttq Fn is not defined')
  }
}

export const triggerConversionMeta = (event:string) => {
  const {fbq} = window
  // Grab the focused button (the one that was clicked)
  const activeElement = document.activeElement as HTMLElement | null
  const buttonId = activeElement?.id || 'unknown'
  if (typeof fbq === 'function') {
    fbq('trackCustom', event)
    console.log(`Conversion fbq for event ${event}`, {buttonId})
  } else {
    console.warn('fbq Fn is not defined')
  }
}
