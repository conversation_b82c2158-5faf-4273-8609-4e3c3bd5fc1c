export const getCid = (): string => {
  const cookies = document.cookie.split(';')
  const gtagCookie = cookies.find(cookie => cookie.trim().startsWith('_ga='))
  if (gtagCookie) {
    return gtagCookie.split('=')[1]
  }
  return 'GA1.2.106822649.1670999798'  // Default value jika tidak ada cookie
}

export const getUtmParams = () => {
  const urlParams = new URLSearchParams(window.location.search)
  return {
    utmSource: urlParams.get('utm_source') || 'google',
    utmMedium: urlParams.get('utm_medium') || 'website',
    utmCampaign: urlParams.get('utm_campaign') || 'space_dunk',
    utmTerm: urlParams.get('utm_term') || 'test',
    utmContent: urlParams.get('utm_content') || 'testing',
  }
}
