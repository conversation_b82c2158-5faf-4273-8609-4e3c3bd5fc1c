export interface CountryCode {
  code: string;
  country: string;
  name: (country: string) => string;
  minLength: number;
  maxLength: number;
  privacyPolicy: string;
  cookiePolicy: string;
  termAndCondition: string;
  contactUs: string;
  socialMedia: {
    instagram: string;
    facebook: string;
    youtube: string;
  };
  ecommerce: {
    shopee: string;
    lazada: string;
    lotus: string;
  };
  launchDate: string;
  biweekly: Array<{
    startDate: string;
    endDate: string;
  }>;
}

const countryCodes: CountryCode[] = [
  {
    code: '+63',
    country: 'PH',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Filipina'
        case 'hk':
        case 'tw':
          return '菲律宾'
        case 'th':
          return 'ฟิลิปปินส์'
        case 'vn':
          return 'Philippines'
        default:
          return 'Philippines'
      }
    },
    minLength: 10,
    maxLength: 11,
    privacyPolicy:
      'https://drive.google.com/file/d/1rIlp6_uChKaSpn1nr4WGycXSxSvb0WZa/view',
    cookiePolicy: '',
    termAndCondition:
      'https://drive.google.com/file/d/1hk3qmF23IgcCm2HPWd1ZeSwUl2We-WeE/view?usp=sharing',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: 'https://www.instagram.com/oreophilippines/',
      facebook: 'https://www.facebook.com/OreoPhilippines/',
      youtube: 'https://www.youtube.com/@oreophilippines',
    },
    ecommerce: {
      shopee: 'https://shopee.ph/mondelezphofficial',
      lazada: 'https://s.lazada.com.ph/s.8WxBv',
      lotus: '',
    },
    launchDate: '2025-04-11',
    biweekly: [
      {
        startDate: '2025-04-11',
        endDate: '2025-04-24',
      },
      {
        startDate: '2025-04-25',
        endDate: '2025-05-08',
      },
      {
        startDate: '2025-05-09',
        endDate: '2025-05-22',
      },
      {
        startDate: '2025-05-23',
        endDate: '2025-06-06',
      },
    ],
  },
  {
    code: '+66',
    country: 'TH',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Thailand'
        case 'hk':
        case 'tw':
          return '泰国'
        case 'th':
          return 'ไทย'
        case 'vn':
          return 'Thailand'
        default:
          return 'Thailand'
      }
    },
    minLength: 9,
    maxLength: 9,
    privacyPolicy: 'https://th.mondelezinternational.com/privacy-policy',
    cookiePolicy:
      'https://docs.google.com/document/d/1t8qV_hPYw2fD2olS4YGpUKf5K4Udv3Uz/edit?usp=sharing&ouid=102273132944069524258&rtpof=true&sd=true',
    termAndCondition:
      'https://drive.google.com/file/d/1iU7Ua4jg1brioSeScIMZqn9r2Si_0iIe/view',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: 'https://www.instagram.com/oreo_thailand/',
      facebook: 'https://www.facebook.com/OreoTH/',
      youtube: 'https://www.youtube.com/@OreoAsia',
    },
    ecommerce: {
      shopee: 'https://shopee.co.th/mondelez_officialstore',
      lazada:
        'https://www.lazada.co.th/shop/mondelez/?path=promotion-41098-0.htm&tab=promotion',
      lotus: '',
    },
    launchDate: '2025-04-11',
    biweekly: [
      {
        startDate: '2025-04-11',
        endDate: '2025-04-24',
      },
      {
        startDate: '2025-04-25',
        endDate: '2025-05-08',
      },
      {
        startDate: '2025-05-09',
        endDate: '2025-05-22',
      },
      {
        startDate: '2025-05-23',
        endDate: '2025-06-06',
      },
      {
        startDate: '2025-06-07',
        endDate: '2025-06-20',
      },
    ],
  },
  {
    code: '+65',
    country: 'SG',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Singapore'
        case 'hk':
        case 'tw':
          return '新加坡'
        case 'th':
          return 'สิงคโปร์'
        case 'vn':
          return 'Singapore'
        default:
          return 'Singapore'
      }
    },
    minLength: 8,
    maxLength: 8,
    privacyPolicy:
      'https://drive.google.com/file/d/10z92Cl8myRjwlj8uxoudcqdMw0_b1BB0/view',
    cookiePolicy: '',
    termAndCondition:
      'https://drive.google.com/file/d/1qgeeYKDA9dIfgr8w_ENv6vDzUgeg7vZr/view',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: 'https://www.instagram.com/oreo.mysg/',
      facebook: 'https://www.facebook.com/OreoMalaysiaSingapore',
      youtube: 'https://www.youtube.com/@OreoAsia',
    },
    ecommerce: {
      shopee: 'https://shopee.sg/mondelezofficialstore',
      lazada:
        'https://pages.lazada.sg/wow/gcp/route/lazada/sg/upr_1000345_lazada/channel/sg/upr-router/sg?hybrid=1&data_prefetch=true&prefetch_replace=1&at_iframe=1&wh_pid=/lazada/channel/sg/redmart-campaign/Oreoo----&useRMContainer=h5&showLoading=false&showTabBar=true&showSearchBar=true&useTabViewStack=true&showUserJourneyWidget=true&tab=home&showRMFrame=true',
      lotus: '',
    },
    launchDate: '2025-04-01',
    biweekly: [
      {
        startDate: '2025-04-01',
        endDate: '2025-04-14',
      },
      {
        startDate: '2025-04-15',
        endDate: '2025-04-30',
      },
      {
        startDate: '2025-05-01',
        endDate: '2025-05-14',
      },
      {
        startDate: '2025-05-15',
        endDate: '2025-05-31',
      },
    ],
  },
  {
    code: '+84',
    country: 'VN',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Vietnam'
        case 'hk':
        case 'tw':
          return '越南'
        case 'th':
          return 'เวียดนาม'
        case 'vn':
          return 'Vietnam'
        default:
          return 'Vietnam'
      }
    },
    minLength: 10,
    maxLength: 10,
    privacyPolicy:
      'https://drive.google.com/drive/folders/1wO4QZ5_w91dj-qDn18IPXVBEVrk0MVTt?usp=sharing',
    cookiePolicy:
      'https://docs.google.com/document/d/1CJwA1KOWv5e-Xap0QDMjpbhtR3io32gh/edit?usp=sharing&ouid=102273132944069524258&rtpof=true&sd=true',
    termAndCondition:
      'https://drive.google.com/file/d/1R4krg6bJDWdC7EkpjnxokQNk9-o-LUoK/view?usp=sharing',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: 'https://www.instagram.com/oreo_vietnam/',
      facebook: 'https://www.facebook.com/OreoVN/',
      youtube: 'https://www.youtube.com/@OreoAsia',
    },
    ecommerce: {
      shopee:
        'https://shopee.vn/universal-link/kinhdo_official_store?deep_and_web=1&utm_campaign=s140360136_ss_vn_webs_oreopokemonmicrosite&utm_source=website&utm_medium=seller&utm_content=oreopokemonmicrosite&smtt=9',
      lazada:
        'https://c.lazada.vn/t/c.0Ep82E?intent=false&fallback=true&url=https%3A%2F%2Fwww.lazada.vn%2Fshop%2Fmondelez-kinh-do',
      lotus: '',
    },
    launchDate: '2025-04-14',
    biweekly: [
      {
        startDate: '2025-04-14',
        endDate: '2025-04-27',
      },
      {
        startDate: '2025-04-28',
        endDate: '2025-05-11',
      },
      {
        startDate: '2025-05-12',
        endDate: '2025-05-25',
      },
      {
        startDate: '2025-05-26',
        endDate: '2025-06-08',
      },
    ],
  },
  {
    code: '+855',
    country: 'KH',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Kamboja'
        case 'hk':
        case 'tw':
          return '柬埔寨'
        case 'th':
          return 'คมบอดีอา'
        case 'vn':
          return 'Cambodia'
        default:
          return 'Cambodia'
      }
    },
    minLength: 8,
    maxLength: 9,
    privacyPolicy: 'https://www.mondelezinternational.com/privacy-policy',
    cookiePolicy: '',
    termAndCondition: '',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: '',
      facebook: 'https://www.facebook.com/OreoCambodia',
      youtube: 'https://www.youtube.com/channel/UCdX13ssfOvjAp_QFFp9-Y-g',
    },
    ecommerce: {
      shopee: '',
      lazada: '',
      lotus: '',
    },
    launchDate: '2025-04-14',
    biweekly: [
      {
        startDate: '2025-04-14',
        endDate: '2025-04-27',
      },
      {
        startDate: '2025-04-28',
        endDate: '2025-05-11',
      },
      {
        startDate: '2025-05-12',
        endDate: '2025-05-25',
      },
      {
        startDate: '2025-05-26',
        endDate: '2025-06-08',
      },
    ],
  },
  {
    code: '+60',
    country: 'MY',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Malaysia'
        case 'hk':
        case 'tw':
          return '马来西亚'
        case 'th':
          return 'มาเลเซีย'
        case 'vn':
          return 'Malaysia'
        default:
          return 'Malaysia'
      }
    },
    minLength: 10,
    maxLength: 11,
    privacyPolicy:
      'https://drive.google.com/file/d/10z92Cl8myRjwlj8uxoudcqdMw0_b1BB0/view',
    cookiePolicy: '',
    termAndCondition:
      'https://drive.google.com/file/d/1hk3qmF23IgcCm2HPWd1ZeSwUl2We-WeE/view?usp=sharing',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: 'https://www.instagram.com/oreo.mysg/',
      facebook: 'https://www.facebook.com/OreoMalaysiaSingapore',
      youtube: 'https://www.youtube.com/@OreoAsia',
    },
    ecommerce: {
      shopee:
        'https://shopee.com.my/universal-link/cadbury_oreo_officialstore?deep_and_web=1&utm_campaign=s59165532_ss_my_webs_oreopokemonmicrosite&utm_source=website&utm_medium=seller&utm_content=oreopokemonmicrosite&smtt=9',
      lazada:
        'https://c.lazada.com.my/t/c.cGNKsf?intent=false&fallback=true&url=https%3A%2F%2Fwww.lazada.com.my%2Fshop%2Fcadbury-oreo-chipsmore-twisties-official-store%2F',
      lotus: '',
    },
    launchDate: '2025-04-01',
    biweekly: [
      {
        startDate: '2025-04-01',
        endDate: '2025-04-14',
      },
      {
        startDate: '2025-04-15',
        endDate: '2025-04-30',
      },
      {
        startDate: '2025-05-01',
        endDate: '2025-05-14',
      },
      {
        startDate: '2025-05-15',
        endDate: '2025-05-31',
      },
    ],
  },
  {
    code: '+62',
    country: 'ID',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Indonesia'
        case 'hk':
        case 'tw':
          return '印尼'
        case 'th':
          return 'อินโดนีเซีย'
        case 'vn':
          return 'Indonesia'
        default:
          return 'Indonesia'
      }
    },
    minLength: 10,
    maxLength: 12,
    privacyPolicy:
      'https://privacy.mondelezinternational.com/id/en-ID/Privacy-Policy/',
    cookiePolicy:
      'https://docs.google.com/document/d/13TKzQgy4CvqTA7JVWzdQt_wp1MTE9i1J/edit?usp=sharing&ouid=102273132944069524258&rtpof=true&sd=true',
    termAndCondition:
      'https://drive.google.com/file/d/1pwmTaaDkRKRoib4-Z-_flK7HaiByS5-x/view',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: 'https://www.instagram.com/oreo_indonesia/',
      facebook: 'https://www.facebook.com/OreoIndonesia',
      youtube: 'https://www.youtube.com/@OreoAsia',
    },
    ecommerce: {
      shopee: 'https://shopee.co.id/mondelezofficial',
      lazada: '',
      // tokopedia: 'https://www.tokopedia.com/mondelez',
      // blibli:
      //   'https://www.blibli.com/merchant/mondelez-official-store/MOZ-48817?pickupPointCode=**********',
      lotus: '',
    },
    launchDate: '2025-04-01',
    biweekly: [
      {
        startDate: '2025-04-01',
        endDate: '2025-04-14',
      },
      {
        startDate: '2025-04-15',
        endDate: '2025-04-30',
      },
      {
        startDate: '2025-05-01',
        endDate: '2025-05-14',
      },
      {
        startDate: '2025-05-15',
        endDate: '2025-05-31',
      },
      {
        startDate: '2025-06-01',
        endDate: '2025-06-15',
      },
    ],
  },
  {
    code: '+679',
    country: 'FJ',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Fiji'
        case 'hk':
        case 'tw':
          return '斐济'
        case 'th':
          return 'ฟิจิ'
        case 'vn':
          return 'Fiji'
        default:
          return 'Fiji'
      }
    },
    minLength: 7,
    maxLength: 7,
    privacyPolicy: 'https://www.mondelezinternational.com/privacy-policy/',
    cookiePolicy: '',
    termAndCondition: '',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: '',
      facebook: 'https://www.facebook.com/OREO/',
      youtube: 'https://www.youtube.com/@OreoAsia',
    },
    ecommerce: {
      shopee: '',
      lazada: '',
      lotus: '',
    },
    launchDate: '2025-04-14',
    biweekly: [
      {
        startDate: '2025-04-14',
        endDate: '2025-04-27',
      },
      {
        startDate: '2025-04-28',
        endDate: '2025-05-11',
      },
      {
        startDate: '2025-05-12',
        endDate: '2025-05-25',
      },
      {
        startDate: '2025-05-26',
        endDate: '2025-06-08',
      },
    ],
  },
  {
    code: '+852',
    country: 'HK',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Hong Kong'
        case 'hk':
        case 'tw':
          return '香港'
        case 'th':
          return 'โฮ่งกง'
        case 'vn':
          return 'Hong Kong'
        default:
          return 'Hong Kong'
      }
    },
    minLength: 8,
    maxLength: 8,
    privacyPolicy:
      'https://drive.google.com/file/d/1UBLPREEqsVF_azFJnayhnA7h1fVqmRi1/view',
    cookiePolicy:
      'https://docs.google.com/document/d/1e7oo1vah6dYMFo4O2ORmeMcIrA_mZN8BqL0UfU09e3o/edit?usp=sharing',
    termAndCondition:
      'https://drive.google.com/file/d/1dBUNURThnl5gOwSSsB41pro-vuXgFiLC/view',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: '',
      facebook: 'https://www.facebook.com/OREO/',
      youtube: 'https://www.youtube.com/@Oreo',
    },
    ecommerce: {
      shopee: '',
      lazada: '',
      lotus: '',
    },
    launchDate: '2025-04-25',
    biweekly: [
      {
        startDate: '2025-04-25',
        endDate: '2025-05-08',
      },
      {
        startDate: '2025-05-09',
        endDate: '2025-05-22',
      },
      {
        startDate: '2025-05-23',
        endDate: '2025-06-05',
      },
      {
        startDate: '2025-06-06',
        endDate: '2025-06-19',
      },
    ],
  },
  {
    code: '+886',
    country: 'TW',
    name: (lang: string) => {
      switch (lang) {
        case 'id':
          return 'Taiwan'
        case 'hk':
        case 'tw':
          return '台湾'
        case 'th':
          return 'ไต้หวัน'
        case 'vn':
          return 'Taiwan'
        default:
          return 'Taiwan'
      }
    },
    minLength: 10,
    maxLength: 10,
    privacyPolicy:
      'https://docs.google.com/document/d/137kAKiFm-gGcJDg9O-aRN2fDYXBoMaE6/edit?usp=sharing&ouid=102273132944069524258&rtpof=true&sd=true',
    cookiePolicy:
      'https://docs.google.com/document/d/1e7oo1vah6dYMFo4O2ORmeMcIrA_mZN8BqL0UfU09e3o/edit?usp=sharing',
    termAndCondition:
      'https://drive.google.com/file/d/1hj7nvgSw0k3oGR5CU_XamCpbqB88l4h-/view?usp=sharing',
    contactUs: 'https://contactus.mdlzapps.com/twistandmake/contact/en-US',
    socialMedia: {
      instagram: '',
      facebook: 'https://www.facebook.com/OreoPlayfulTW/',
      youtube: 'https://www.youtube.com/@Oreo',
    },
    ecommerce: {
      shopee:
        'https://shopee.tw/universal-link/mondelez_tw?deep_and_web=1&utm_campaign=s1064311823_ss_tw_webs_oreo-pokemon-website&utm_source=website&utm_medium=seller&utm_content=pokemon&smtt=9',
      lazada: '',
      lotus: '',
    },
    launchDate: '2025-04-18',
    biweekly: [
      {
        startDate: '2025-04-18',
        endDate: '2025-05-01',
      },
      {
        startDate: '2025-05-02',
        endDate: '2025-05-15',
      },
      {
        startDate: '2025-05-16',
        endDate: '2025-05-29',
      },
      {
        startDate: '2025-05-30',
        endDate: '2025-06-12',
      },
    ],
  },
]

export const getCurrentCountry = (
  country: string | undefined | null = 'sg'
) => getAllCountryCodes().find(
  c => c.country.toLowerCase() === country?.toLowerCase()
)
export const getAllCountryCodes = () => {
  const currentDate = new Date('2025-05-01')
  return countryCodes.filter(countryCode => new Date(countryCode.launchDate) <= currentDate)
}

export const getCurrentBiWeekly = (
  country: string | undefined | null = 'sg'
) => {
  const currentCountry = getCurrentCountry(country)
  if (!currentCountry) return null
  const currentDate = new Date()
  const currentBiWeekly = currentCountry.biweekly.find((biweekly) => {
    const startDate = new Date(biweekly.startDate)
    const endDate = new Date(biweekly.endDate)
    endDate.setHours(23, 59, 59, 999)
    return currentDate >= startDate && currentDate <= endDate
  })
  return currentBiWeekly
}
