declare let React: any

const {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback, ReactNode,
} = React
const music = require('../../assets/audio/oreo-bgm-v3.mp3')

interface MusicContextType {
  isMusicOn: boolean;
  toggleMusic: () => void;
}

const MusicContext = createContext(undefined)

export const MusicProvider = ({
  children,
}) => {
  const [isMusicOn, setIsMusicOn] = useState(true)
  const [audio] = useState(() => {
    const bgm = new Audio(music)
    bgm.loop = true
    bgm.volume = 0.3
    return bgm
  })
  useEffect(() => {
    // Coba putar musik saat komponen dimount
    audio.play().catch(() => {
      // Jika gagal, tambahkan event listener untuk click pertama
      console.log('gagal')
      document.addEventListener(
        'click',
        () => {
          audio.play().catch(console.error)
        },
        {once: true}
      )
    })
    return () => {
      audio.pause()
    }
  }, [audio])

  // Handle visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        audio.pause()
      } else if (isMusicOn) {
        audio.play().catch(console.error)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [audio, isMusicOn])

  // Effect untuk menangani perubahan status musik
  useEffect(() => {
    if (isMusicOn) {
      audio.play().catch(console.error)
    } else {
      audio.pause()
    }
  }, [isMusicOn, audio])

  const toggleMusic = useCallback(() => {
    setIsMusicOn(prev => !prev)
  }, [])

  return (
    <MusicContext.Provider value={{isMusicOn, toggleMusic}}>
      {children}
    </MusicContext.Provider>
  )
}

export const useMusic = () => {
  const context = useContext(MusicContext)
  if (context === undefined) {
    throw new Error('useMusic must be used within a MusicProvider')
  }
  return context
}
