// const BASE_URL ='https://oreo-space-api-production.onrender.com'
const BASE_URL = 'https://oreo-space-api-staging.onrender.com'
const AIBE_URL_RENDER = 'https://oreo-scanning-api.onrender.com/upload'
const AIBE_URL_RUNPOD = 'https://api.runpod.ai/v2/r92d0p6zparmyi/runsync'
const BASE_URL_CMS = 'https://api.cms.mbhumans.com'

export const API_VERIFY_OTP = `${BASE_URL}/api/v1/otp/verify`
export const API_REFRESH_TOKEN = `${BASE_URL}/api/v1/users/refresh-token`
export const API_LOGIN = `${BASE_URL}/api/v1/users/login`
export const API_GENERATE_OTP = `${BASE_URL}/api/v1/otp/generate`
export const API_LEADERBOARD = `${BASE_URL}/api/v1/external/leaderboard`
export const API_SUBMIT_FORM = `${BASE_URL}/api/v1/forms/submit`
export const API_USER_LEADERBOARD = `${BASE_URL}/api/v1/external/leaderboard/user`
export const API_GET_USER = `${BASE_URL}/api/v1/users/me`
export const API_CHECK_PHOTO = `${AIBE_URL_RUNPOD}`
export const API_SEND_DATA_TO_CMS = `${BASE_URL_CMS}/api/v1/public/hooks/participant-submission` 
export const API_GET_API_COUNT = `${BASE_URL_CMS}/api/v1/public/hooks/projects-api-counts/get` 
export const API_UPDATE_API_COUNT = `${BASE_URL_CMS}/api/v1/public/hooks/projects-api-counts/update`
