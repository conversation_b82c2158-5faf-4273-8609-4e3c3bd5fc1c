declare let React: any
declare let ReactRouterDOM: any

const {<PERSON>rows<PERSON><PERSON>outer, Route, Switch, Redirect, useParams} = ReactRouterDOM
import {getAllCountryCodes} from './config/countryCode'
import {Home} from './home'
import {appBase} from '../lib/routes'
const base = appBase()

function ValidatedRoute() {
  const {country} = useParams()
  const isValidCountry = getAllCountryCodes().some(
    code => code.country.toLowerCase() === country?.toLowerCase()
  )

  if (!isValidCountry) {
    return <Redirect to={`${base}/`} />
  }

  return <Home />
}

export default ValidatedRoute
