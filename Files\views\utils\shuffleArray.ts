export function shuffleArrayWithRules(
  arr: number[],
  prevArr: number[]
): number[] {
  let newArr: number[]
  const lastIndex = arr.length - 1
  let attempts = 100  // Batas percobaan untuk menghindari loop tak terbatas

  do {
    newArr = [...arr]
    newArr.splice(lastIndex, 1)  // Hilangkan elemen terakhir

    // Acak elemen 0-3 den<PERSON>
    for (let i = newArr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
;[newArr[i], newArr[j]] = [newArr[j], newArr[i]]
    }

    // Masukkan kembali elemen terakhir pada posisinya
    newArr.push(arr[lastIndex])

    attempts--
  } while (
    newArr[3] === prevArr[0] ||  // Aturan 1: nilai di index 3 tidak boleh ke index 0
    newArr.some((val, idx) => idx < lastIndex && val === prevArr[idx]) ||  // Aturan 2: tidak boleh di posisi yang sama
    newArr[0] === prevArr[3]  // Aturan tambahan: nilai sebelumnya di index 3 hanya boleh ke index 1 atau 2
  )

  return newArr
}

// Contoh penggunaan
// const initialArray = [1, 2, 3, 4, 5];
// let prevArray = [...initialArray];

// for (let i = 0; i < 20; i++) {
//   let newArray = shuffleArrayWithRules(initialArray, prevArray);
//   console.log(newArray);
//   prevArray = newArray;
// }
