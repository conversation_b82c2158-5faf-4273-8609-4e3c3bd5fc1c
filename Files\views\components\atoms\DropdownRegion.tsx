declare let React: any
declare let ReactRouterDOM: any
const {useState, useRef, useEffect} = React
const {useHistory, useParams} = ReactRouterDOM
import {appBase} from '../../../lib/routes'
const base = appBase()
import {
  getAllCountryCodes,
  getCurrentCountry,
} from '../../config/countryCode'

interface Option {
  code: string;
  country: string;
  name: (country: string) => string;
  minLength: number;
  maxLength: number;
}

interface DropdownRegionProps {
  onSelect?: (code: string) => void;
  disabled?: boolean;
  signOut: () => void;
}

const DropdownRegion = ({
  onSelect,
  disabled = false,
  signOut,
}: DropdownRegionProps) => {
  const {country} = useParams()

  const [selectedOption, setSelectedOption] = useState(
    country ? getCurrentCountry(country)?.name(country) : ''
  )

  useEffect(() => {
    setSelectedOption(country ? getCurrentCountry(country)?.name(country) : '')
  }, [country])

  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)
  const history = useHistory()

  const toggleDropdown = () => {
    if (disabled) return
    setIsOpen(prev => !prev)
  }

  const handleOptionClick = (option: Option) => {
    if (disabled) return
    onSelect?.(option.country)
    setSelectedOption(option.name(country))
    setIsOpen(false)
    signOut()
    window.location.href = `${base}/${option.country.toLowerCase()}`
    // history.push(`${base}/${option.country.toLowerCase()}`)
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className='relative w-full max-w-[250px]' ref={dropdownRef}>
      <div
        style={{
          backdropFilter: 'blur(7.5px)',
          backgroundColor: 'rgba(0, 23, 84, 0.6)',
        }}
        onClick={toggleDropdown}
        className={`p-2 px-4 border-2   text-white ${
          disabled ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'
        } text-[12px] flex justify-between items-center border-[#1D46B0]`}
      >
        <span>{selectedOption || 'Select Country'}</span>
        {!disabled && (
          <span className='ml-2'>
            <svg
              width='17'
              height='10'
              viewBox='0 0 17 10'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M7.71265 9.3684L0.818596 2.25481C0.723506 2.15669 0.652505 2.05072 0.605594 1.9369C0.558683 1.82308 0.534593 1.70011 0.533325 1.56798C0.533325 1.30633 0.620808 1.07738 0.795775 0.881147C0.970741 0.68491 1.20023 0.586792 1.48423 0.586792H15.938C16.2232 0.586792 16.4534 0.68491 16.6283 0.881147C16.8033 1.07738 16.8901 1.30633 16.8889 1.56798C16.8889 1.63339 16.7938 1.86233 16.6036 2.25481L9.70955 9.3684C9.55106 9.53193 9.39258 9.6464 9.23409 9.71181C9.07561 9.77723 8.90128 9.80993 8.7111 9.80993C8.52092 9.80993 8.34658 9.77723 8.1881 9.71181C8.02962 9.6464 7.87113 9.53193 7.71265 9.3684Z'
                fill='white'
              />
            </svg>
          </span>
        )}
      </div>
      {isOpen && !disabled && (
        <div
          style={{
            backdropFilter: 'blur(7.5px)',
            backgroundColor: 'rgba(0, 23, 84, 0.6)',
          }}
          className='absolute z-10 w-full overflow-auto'
        >
          {getAllCountryCodes().map(option => (
            <div
              key={option.country}
              onClick={() => handleOptionClick(option)}
              className={`p-2 border    text-white ${
                disabled ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'
              } text-[12px] flex justify-between items-center border-[#1D46B0]`}
            >
              {option.name(country)}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default DropdownRegion
