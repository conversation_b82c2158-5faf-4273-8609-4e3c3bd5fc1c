declare let window: any
declare let React: any
const {useState, useEffect} = React
declare let ReactRouterDOM: any
const {useHistory, useParams} = ReactRouterDOM
import {appBase} from '../../../lib/routes'
const base = appBase()
import {
  CountryCode,
  getAllCountryCodes,
  getCurrentCountry,
} from '../../config/countryCode'
import {CountrySelection} from '../../analytics/events'
import {translate, Language} from '../../config/translation'

const Menu = ({
  onClose,
  isOpen,
  goToPage,
  signOut,
}: {
onClose: () => void;
isOpen: boolean;
goToPage: (page: string) => void;
signOut: () => void;
}) => {
  const {country} = useParams()
  const history = useHistory()
  const [region, setRegion] = useState(
    country ? getCurrentCountry(country)?.name(country) : ''
  )

  const [countryData, setCountryData] = useState(null)

  useEffect(() => {
    setCountryData(getCurrentCountry(country) as CountryCode)
  }, [country])

  const handleRegionClick = (data: {
  code: string;
  country: string;
  name: (lang:string)=>string;
  minLength: number;
  maxLength: number;
  }) => {
    setRegion(data.name(country))
    history.push(`${base}/${data.country?.toLowerCase()}`)
    onClose()
  }

  const handlePageClick = (page: string) => {
    goToPage(page)
    onClose()
  }

  useEffect(() => {
    setRegion(country ? getCurrentCountry(country)?.name(country) : '')
  }, [country])

  // useEffect(() => {
  //   if (window.ttq) {
  //     console.log('ttq track')
  //     window.ttq.track('CountrySelection')
  //   }
  //   console.log('ttq empty ---')
  // }, [])

  return (
    <div
      className={`${
        isOpen ? 'open' : 'closed'
      } fixed inset-0 bg-blue-900 text-white z-[100] flex flex-col items-center py-4 overflow-y-auto`}
    >
      {/* transition-transform transform translate-y-full open:translate-y-0 */}
      <div className='relative w-full flex items-center justify-center mb-6'>
        <button
          onClick={onClose}
          className='absolute top-1/2 -translate-y-1/2 right-4 self-end text-xl h-5 w-5 border-2 border-white rounded-full flex items-center justify-center'
        >
          ×
        </button>
        <h2 className='text-lg'>
          [{translate('chooseRegion', country as Language)}]
        </h2>
      </div>
      <ul className='font-bold flex flex-col gap-4 justify-center items-center mb-8 text-center text-xl'>
        {getAllCountryCodes().map(item => (
          <li
            key={item.name(country)}
            id='Country_Selection'
            className={`${
              item.name(country) === region ? '' : 'text-[#5379DB]'
            } flex items-center gap-2 relative hover:text-white transition-all duration-300 cursor-pointer`}
            onClick={(e) => {
              handleRegionClick(item)
              CountrySelection()
            }}
          >
            {item.name(country) === region && (
              <svg
                className='absolute -left-8'
                width='12'
                height='22'
                viewBox='0 0 12 22'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M11.1912 10.908L0.584559 21.5146L0.584562 0.301357L11.1912 10.908Z'
                  fill='white'
                />
              </svg>
            )}

            {item.name(country)}
          </li>
        ))}
      </ul>
      <div className='border border-white w-full mb-8'></div>
      <div className='flex flex-col gap-4 text-center font-bold'>
        <button
          onClick={() => handlePageClick('how-to-participate')}
          className='underline'
        >
          {translate('howToParticipate', country as Language)}
        </button>
        <button
          onClick={() => handlePageClick('leaderboard')}
          className='underline'
        >
          {translate('seeTopPlayer', country as Language)}
        </button>
        <button
          id='ThePrizes'
          className='underline'
          onClick={() => handlePageClick('the-prizes')}
        >
          {translate('thePrizes', country as Language)}
        </button>
        <button
          onClick={() => handlePageClick('oreo-space-dunk')}
          className='underline'
        >
          {translate('oreoSpaceDunk', country as Language)}
        </button>
        <a href='https://www.oreo.com/contact' className='underline' target='_blank'>
          {translate('contactUs', country as Language)}
        </a>
        <a href='https://www.mondelezinternational.com/privacy-policy/' className='underline' target='_blank'>
          {translate('privacyPolicy', country as Language)}
        </a>
        <span
          className='underline'
          onClick={() => {
            handlePageClick('cookie-policy')
          }}
        >
          {translate('cookiePolicy', country as Language)}
        </span>
        <a href='#' className='underline' target='_blank'>
          {translate('termsAndConditions', country as Language)}
        </a>
      </div>
      <div className='flex space-x-8 mt-8'>
        {countryData?.socialMedia.facebook && (
          <a
            href={countryData?.socialMedia.facebook}
            target='_blank'
            rel='noopener noreferrer'
            id='facebook'
          >
            <svg
              className='w-10 h-10'
              width='40'
              height='41'
              viewBox='0 0 40 41'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M20 0.650024C8.99999 0.650024 0 9.66609 0 20.7705C0 30.8107 7.31999 39.144 16.88 40.65V26.5938H11.8V20.7705H16.88V16.3328C16.88 11.2926 19.86 8.52151 24.44 8.52151C26.62 8.52151 28.9 8.90304 28.9 8.90304V13.8629H26.38C23.9 13.8629 23.12 15.4091 23.12 16.9954V20.7705H28.68L27.78 26.5938H23.12V40.65C27.8329 39.9027 32.1244 37.4884 35.2198 33.8429C38.3153 30.1974 40.0107 25.561 40 20.7705C40 9.66609 31 0.650024 20 0.650024Z'
                fill='white'
              />
            </svg>
          </a>
        )}
        {countryData?.socialMedia.instagram && (
          <a
            href={countryData?.socialMedia.instagram}
            target='_blank'
            rel='noopener noreferrer'
            id='instagram'
          >
            <svg
              className='w-10 h-10'
              width='40'
              height='41'
              viewBox='0 0 40 41'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M22.3504 2.65002C24.3753 2.65542 25.4031 2.66622 26.2904 2.69142L26.6396 2.70402C27.0428 2.71842 27.4406 2.73642 27.9211 2.75802C29.8362 2.84802 31.143 3.15041 32.2895 3.595C33.4775 4.05219 34.4782 4.67138 35.479 5.67035C36.3946 6.56985 37.1028 7.65835 37.5543 8.85987C37.9988 10.0064 38.3012 11.3132 38.3912 13.2302C38.4128 13.709 38.4308 14.1067 38.4452 14.5117L38.456 14.8609C38.483 15.7465 38.4938 16.7743 38.4974 18.7992L38.4992 20.142V22.4999C38.5036 23.8128 38.4898 25.1257 38.4578 26.4382L38.447 26.7874C38.4326 27.1924 38.4146 27.5902 38.393 28.069C38.303 29.986 37.997 31.2909 37.5543 32.4393C37.1028 33.6408 36.3946 34.7293 35.479 35.6288C34.5795 36.5445 33.491 37.2527 32.2895 37.7042C31.143 38.1488 29.8362 38.4512 27.9211 38.5412L26.6396 38.5952L26.2904 38.606C25.4031 38.6312 24.3753 38.6438 22.3504 38.6474L21.0077 38.6492H18.6516C17.3381 38.6538 16.0247 38.64 14.7116 38.6078L14.3624 38.597C13.9351 38.5808 13.508 38.5622 13.0809 38.5412C11.1658 38.4512 9.85905 38.1488 8.71071 37.7042C7.50987 37.2525 6.42202 36.5442 5.52307 35.6288C4.60682 34.7295 3.89795 33.641 3.44598 32.4393C3.0014 31.2927 2.69901 29.986 2.60902 28.069L2.55502 26.7874L2.54602 26.4382C2.51284 25.1257 2.49784 23.8129 2.50102 22.4999V18.7992C2.49604 17.4863 2.50924 16.1735 2.54062 14.8609L2.55322 14.5117C2.56762 14.1067 2.58562 13.709 2.60722 13.2302C2.69721 11.3132 2.9996 10.0082 3.44418 8.85987C3.8972 7.65786 4.60732 6.56931 5.52487 5.67035C6.4233 4.75516 7.51051 4.04692 8.71071 3.595C9.85905 3.15041 11.164 2.84802 13.0809 2.75802C13.5597 2.73642 13.9592 2.71842 14.3624 2.70402L14.7116 2.69322C16.0241 2.66124 17.3369 2.64744 18.6498 2.65182L22.3504 2.65002ZM20.5001 11.6498C18.1133 11.6498 15.8242 12.598 14.1365 14.2858C12.4487 15.9736 11.5006 18.2627 11.5006 20.6496C11.5006 23.0365 12.4487 25.3256 14.1365 27.0134C15.8242 28.7012 18.1133 29.6494 20.5001 29.6494C22.8869 29.6494 25.176 28.7012 26.8638 27.0134C28.5515 25.3256 29.4997 23.0365 29.4997 20.6496C29.4997 18.2627 28.5515 15.9736 26.8638 14.2858C25.176 12.598 22.8869 11.6498 20.5001 11.6498ZM20.5001 15.2497C21.2092 15.2496 21.9114 15.3892 22.5666 15.6604C23.2217 15.9317 23.8171 16.3293 24.3186 16.8307C24.8201 17.332 25.2179 17.9272 25.4894 18.5823C25.7608 19.2374 25.9006 19.9396 25.9007 20.6487C25.9009 21.3578 25.7613 22.06 25.4901 22.7152C25.2188 23.3704 24.8212 23.9657 24.3198 24.4672C23.8185 24.9687 23.2233 25.3666 22.5682 25.6381C21.9132 25.9096 21.211 26.0493 20.5019 26.0495C19.0698 26.0495 17.6964 25.4805 16.6837 24.4679C15.6711 23.4552 15.1022 22.0817 15.1022 20.6496C15.1022 19.2175 15.6711 17.844 16.6837 16.8313C17.6964 15.8186 19.0698 15.2497 20.5019 15.2497M29.9514 8.94987C29.3547 8.94987 28.7825 9.18692 28.3605 9.60887C27.9386 10.0308 27.7016 10.6031 27.7016 11.1998C27.7016 11.7965 27.9386 12.3688 28.3605 12.7908C28.7825 13.2127 29.3547 13.4498 29.9514 13.4498C30.5481 13.4498 31.1204 13.2127 31.5423 12.7908C31.9643 12.3688 32.2013 11.7965 32.2013 11.1998C32.2013 10.6031 31.9643 10.0308 31.5423 9.60887C31.1204 9.18692 30.5481 8.94987 29.9514 8.94987Z'
                fill='white'
              />
            </svg>
          </a>
        )}
        {countryData?.socialMedia.youtube && (
          <a
            href={countryData?.socialMedia.youtube}
            target='_blank'
            rel='noopener noreferrer'
            id='youtube'
          >
            <svg
              className='w-10 h-10'
              width='40'
              height='41'
              viewBox='0 0 40 41'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M23.3396 20.3021L18.6604 18.1188C18.2521 17.9292 17.9167 18.1417 17.9167 18.5938V22.7063C17.9167 23.1584 18.2521 23.3709 18.6604 23.1813L23.3375 20.9979C23.7479 20.8063 23.7479 20.4938 23.3396 20.3021ZM20 0.650024C8.95417 0.650024 0 9.60419 0 20.65C0 31.6959 8.95417 40.65 20 40.65C31.0458 40.65 40 31.6959 40 20.65C40 9.60419 31.0458 0.650024 20 0.650024ZM20 28.775C9.7625 28.775 9.58333 27.8521 9.58333 20.65C9.58333 13.4479 9.7625 12.525 20 12.525C30.2375 12.525 30.4167 13.4479 30.4167 20.65C30.4167 27.8521 30.2375 28.775 20 28.775Z'
                fill='white'
              />
            </svg>
          </a>
        )}
      </div>
      <button
        className='font-bold mt-8'
        onClick={() => {
          signOut()
          onClose()
        }}
      >
        {translate('signOut', country as Language)}
      </button>

    </div>
  )
}

export default Menu
