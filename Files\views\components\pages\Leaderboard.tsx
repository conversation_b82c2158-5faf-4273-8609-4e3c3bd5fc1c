declare let React: any
declare let ReactRouterDOM: any
const {useState, useEffect, useRef, useCallback} = React
import {API_LEADERBOARD, API_USER_LEADERBOARD} from '../../config/apiConfig'
import {getBiWeeklyDates} from '../../utils/getBiWeeklyDates'
import Title from '../atoms/Title'
const {useParams} = ReactRouterDOM
import {formatNumber} from '../../utils/formatNumber'
import {
  Language,
  translate,
  translatePositionAndPoints,
} from '../../config/translation'

import {PrizeType} from '../../config/getPrizeByRegion'
import getPrizeByRegion from '../../config/getPrizeByRegion'
import {getCurrentBiWeekly} from '../../config/countryCode'

interface LeaderboardUser {
  total_points: string;
  id: number;
  hubspot_id: string;
  first_name: string;
  last_name: string;
  username: string;
  phone: string;
  date_of_birth: string | null;
  level: number;
  latest_usage_bonus_level: string | null;
  is_active: boolean;
}

interface PaginationInfo {
  page: number;
  limit: number;
  totalData: string;
  totalPage: number;
}

interface UserLeaderboardResponse {
  position: string;
  total_points: string;
  id: number;
  first_name: string;
  last_name: string;
}

const Leaderboard = () => {
  const {country} = useParams()

  const [filter, setFilter] = useState('allTime')
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const observer = useRef()
  const LIMIT = 20
  const widthRef = useRef(null)
  const [width, setWidth] = useState(0)
  const [userLeaderboard, setUserLeaderboard] = useState(null)

  const lastUserElementRef = useCallback(
    (node: any) => {
      if (loading) return
      if (observer.current) observer.current.disconnect()

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore && users.length < 100) {
          setPage(prevPage => prevPage + 1)
        }
      })

      if (node) observer.current.observe(node)
    },
    [loading, hasMore]
  )

  const currentBiWeekly = getCurrentBiWeekly(country)

  useEffect(() => {
    const fetchUsers = async () => {
      if (users.length >= LIMIT) return
      setLoading(true)

      const baseUrl = API_LEADERBOARD

      const weeklyParams =
        filter === 'weekly'
          ? `&weekFrom=${currentBiWeekly?.startDate}&weekTo=${
            currentBiWeekly?.endDate
          }`
          : ''

      const url = `${baseUrl}?order=desc&page=${page}&limit=${LIMIT}${weeklyParams}&region=${country?.toLocaleLowerCase()}`

      try {
        const response = await fetch(url)
        const data = await response.json()
        if (data.status === 200) {
          const paginationInfo: PaginationInfo = data.pagination

          setUsers((prevUsers) => {
            if (page === 1) return data.data
            return [...prevUsers, ...data.data]
          })
          setHasMore(page < paginationInfo.totalPage)
        }
      } catch (error) {
        console.error('Error fetching leaderboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [filter, page])

  useEffect(() => {
    if (widthRef.current) {
      const width = widthRef.current.offsetWidth
      setWidth(width)
      console.log('Lebar elemen:', width)
    }
  }, [users])

  const handleFilterChange = (newFilter: 'weekly' | 'allTime') => {
    setFilter(newFilter)
    setPage(1)
    setUsers([])
    setHasMore(true)
  }

  const maxPoints = parseInt(users[0]?.total_points)
  const minPoints = 0

  const truncateName = (firstName: string, lastName: string) => {
    const fullName = `${firstName} ${lastName}`
    return fullName.length > 15 ? `${fullName.substring(0, 15)}...` : fullName
  }

  // Tambahkan fungsi untuk fetch data user leaderboard
  const fetchUserLeaderboard = async () => {
    const token = localStorage.getItem('token')
    if (!token) return

    try {
      const weeklyParams =
        filter === 'weekly'
          ? `?weekFrom=${getBiWeeklyDates().weekFrom}&weekTo=${
            getBiWeeklyDates().weekTo
          }`
          : ''

      const response = await fetch(`${API_USER_LEADERBOARD}${weeklyParams}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      const data = await response.json()

      if (data.status === 200) {
        setUserLeaderboard(data.data)
      }
    } catch (error) {
      console.error('Error fetching user leaderboard:', error)
    }
  }

  // Panggil fetchUserLeaderboard saat filter berubah
  useEffect(() => {
    fetchUserLeaderboard()
  }, [filter])

  const formatNumberWithDots = (num: number) => num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.')

  // Calculate width for user leaderboard if outside the top 100
  let widthPx = 0
  let widthPercentage = 0
  if (userLeaderboard) {
    widthPercentage =
      ((parseInt(userLeaderboard.total_points) - minPoints) /
        (maxPoints - minPoints)) *
      100

    widthPx = (width - 50) * (widthPercentage / 100)
  }

  const alltimePrize = getPrizeByRegion(country, 'alltime') as PrizeType
  const weeklyPrize = getPrizeByRegion(country, 'biweekly') as PrizeType
  const isOnlyAlltime = weeklyPrize.prize === ''

  return (
    <div className='w-full max-w-[400px] flex flex-col gap-4 items-center justify-center px-4'>
      <div className='flex flex-col items-center justify-center'>
        {filter === 'allTime' ? (
          <img
            src={alltimePrize.prize}
            alt='alltimePrize'
            className=' max-h-[110px]'
          />
        ) : (
          <img
            src={weeklyPrize.prize}
            alt='weeklyPrize'
            className=' max-h-[110px]'
          />
        )}
        <Title text={translate('leaderboard', country as Language)} className='mt-2 fade-in' />
        {userLeaderboard && (
          <p className='fade-in text-center text-xs mb-2'>
            {translatePositionAndPoints(
              country as Language,
              parseInt(userLeaderboard?.position || '0'),
              formatNumber(parseInt(userLeaderboard?.total_points || '0'))
            )}
          </p>
        )}
      </div>
      <div className='flex gap-2 w-full font-medium text-[10px]'>
        {!isOnlyAlltime && (
          <button
          id='weekly'
          className={`uppercase px-4 py-1 border border-white w-full ${
            filter === 'weekly'
              ? 'bg-white text-[#00227A]'
              : 'bg-transparent text-white opacity-50'
          }`}
          onClick={() => handleFilterChange('weekly')}
        >
            {translate('biWeekly', country as Language)}
          </button>
        )}
        <button
          id='all-time'
          className={`uppercase px-4 py-1 border border-white w-full ${
            filter === 'allTime'
              ? 'bg-white text-[#00227A]'
              : 'bg-transparent text-white opacity-50'
          }`}
          onClick={() => handleFilterChange('allTime')}
        >
          {translate('allTime', country as Language)}
        </button>
      </div>

      <div className='flex items-center w-full text-[10px]'>
        <div className='w-1/2 flex items-center justify-start'>
          <div className='w-10 mr-2'></div>
          <span className='font-medium w-full text-center'>{translate('name', country as Language)}</span>
        </div>
        <div className='w-1/2 text-center'>{translate('points', country as Language)}</div>
      </div>

      <div className='font-bold text-xs space-y-4 w-full   overflow-y-auto responsive-leaderboard'>
        {users.map((user, i) => {
          let position: any = i + 1

          if (userLeaderboard && user.id === userLeaderboard.id) {
            position = parseInt(userLeaderboard.position)
          }
          if (i === 0) position = One
          if (i === 1) position = Two
          if (i === 2) position = Three

          const widthPercentage =
            ((parseInt(user.total_points) - minPoints) /
              (maxPoints - minPoints)) *
            100

          const widthPx = (width - 50) * (widthPercentage / 100)
          const ref = i === users.length - 1 ? lastUserElementRef : null

          return (
            <div
              ref={ref}
              key={`${user.username}-${i}`}
              className={'flex gap-2 items-center w-full fade-in'}
            >
              <div className='w-1/2 flex items-center justify-start'>
                <div className='w-10 flex justify-center text-center mr-2'>
                  {position}
                </div>
                <span className='w-full truncate'>
                  {truncateName(user.first_name, user.last_name)}
                </span>
              </div>

              <div className='w-1/2' ref={widthRef}>
                <div
                   className={`text-sm font-semibold py-1 px-3 border  ${
                     userLeaderboard && user.id === userLeaderboard.id
                       ? 'bg-white text-[#1D46B0] border-[#1D46B0]'
                       : 'bg-gradient-to-r from-[#0F2869] to-[#1D46B0] border-white text-white '
                   }`}
                  style={{
                    width: `${widthPx + 50}px`,  // Masih menggunakan style untuk width
                  }}
                >
                  {formatNumber(parseInt(user.total_points))}
                </div>
              </div>
            </div>
          )
        })}

        {/* Show user leaderboard if outside the top 100 */}
        {users.length >= LIMIT &&
          userLeaderboard &&
          parseInt(userLeaderboard.position) > LIMIT && (
            <div
              // ref={ref}
              key={`${userLeaderboard.id}`}
              className={'flex gap-2 items-center w-full fade-in'}
            >
              <div className='w-1/2 flex items-center justify-start'>
                <div className='w-10 flex justify-center text-center mr-2'>
                  {userLeaderboard.position}
                </div>
                <span className='w-full truncate'>
                  {truncateName(
                    userLeaderboard.first_name,
                    userLeaderboard.last_name
                  )}
                </span>
              </div>

              <div className='w-1/2' ref={widthRef}>
                <div
                  className={`text-sm font-semibold py-1 px-3 border  ${
                    userLeaderboard && userLeaderboard.id === userLeaderboard.id
                      ? 'bg-white text-[#1D46B0] border-[#1D46B0]'
                      : 'bg-gradient-to-r from-[#0F2869] to-[#1D46B0] border-white text-white '
                  }`}
                  style={{
                    width: `${widthPx + 50}px`,  // Masih menggunakan style untuk width
                  }}
                >
                  {formatNumber(parseInt(userLeaderboard.total_points))}
                </div>
              </div>
            </div>
        )}
        {loading && (
          <div className='text-center py-10'>
            <svg
              aria-hidden='true'
              role='status'
              className='inline w-10 h-10 me-3 text-white animate-spin'
              viewBox='0 0 100 101'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z'
                fill='#E5E7EB'
              />
              <path
                d='M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z'
                fill='currentColor'
              />
            </svg>
          </div>
        )}
      </div>
    </div>
  )
}

export default Leaderboard
// Definisi SVG untuk posisi 1, 2, dan 3 tetap tidak berubah
const One = (
  <svg
    width='20'
    height='10'
    viewBox='0 0 20 10'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <mask
      id='path-1-outside-1_846_1650'
      maskUnits='userSpaceOnUse'
      x='-0.331543'
      y='0'
      width='21'
      height='10'
      fill='black'
    >
      <rect fill='white' x='-0.331543' width='21' height='10' />
      <path d='M0.824457 2.832L4.36446 0.84H5.84046V9H3.82446V3.132L1.58046 4.356L0.824457 2.832ZM12.768 3.072L12.252 4.68C11.664 4.368 10.968 4.176 10.38 4.176C9.97202 4.176 9.62402 4.296 9.62402 4.596C9.62402 4.836 9.78002 4.92 10.14 5.016L11.256 5.316C12.396 5.628 12.756 6.24 12.756 7.092C12.756 8.304 11.796 9.12 10.14 9.12C9.03602 9.12 8.14802 8.82 7.66802 8.58L8.04002 7.008C8.61602 7.308 9.36002 7.596 9.99602 7.596C10.464 7.596 10.74 7.5 10.74 7.224C10.74 7.02 10.5 6.912 10.104 6.804L9.10802 6.528C8.08802 6.228 7.58402 5.664 7.58402 4.716C7.58402 3.468 8.67602 2.616 10.428 2.616C11.256 2.616 12.276 2.844 12.768 3.072ZM15.0524 1.128H16.7564V2.724H18.9284V4.284H16.7564V6.468C16.7564 7.104 17.0204 7.368 17.5004 7.368C17.9324 7.368 18.3884 7.152 18.6764 6.948L19.2884 8.484C18.6644 8.868 17.8364 9.12 16.8524 9.12C15.5444 9.12 14.7404 8.328 14.7404 6.816V4.284H13.6724V2.724H14.8004L15.0524 1.128Z' />
    </mask>
    <path
      d='M0.824457 2.832L4.36446 0.84H5.84046V9H3.82446V3.132L1.58046 4.356L0.824457 2.832ZM12.768 3.072L12.252 4.68C11.664 4.368 10.968 4.176 10.38 4.176C9.97202 4.176 9.62402 4.296 9.62402 4.596C9.62402 4.836 9.78002 4.92 10.14 5.016L11.256 5.316C12.396 5.628 12.756 6.24 12.756 7.092C12.756 8.304 11.796 9.12 10.14 9.12C9.03602 9.12 8.14802 8.82 7.66802 8.58L8.04002 7.008C8.61602 7.308 9.36002 7.596 9.99602 7.596C10.464 7.596 10.74 7.5 10.74 7.224C10.74 7.02 10.5 6.912 10.104 6.804L9.10802 6.528C8.08802 6.228 7.58402 5.664 7.58402 4.716C7.58402 3.468 8.67602 2.616 10.428 2.616C11.256 2.616 12.276 2.844 12.768 3.072ZM15.0524 1.128H16.7564V2.724H18.9284V4.284H16.7564V6.468C16.7564 7.104 17.0204 7.368 17.5004 7.368C17.9324 7.368 18.3884 7.152 18.6764 6.948L19.2884 8.484C18.6644 8.868 17.8364 9.12 16.8524 9.12C15.5444 9.12 14.7404 8.328 14.7404 6.816V4.284H13.6724V2.724H14.8004L15.0524 1.128Z'
      fill='url(#paint0_linear_846_1650)'
    />
    <path
      d='M0.824457 2.832L0.579256 2.39625L0.165621 2.62901L0.37654 3.05419L0.824457 2.832ZM4.36446 0.84V0.34H4.23344L4.11926 0.404252L4.36446 0.84ZM5.84046 0.84H6.34046V0.34H5.84046V0.84ZM5.84046 9V9.5H6.34046V9H5.84046ZM3.82446 9H3.32446V9.5H3.82446V9ZM3.82446 3.132H4.32446V2.28973L3.58503 2.69305L3.82446 3.132ZM1.58046 4.356L1.13254 4.57819L1.36354 5.04386L1.81988 4.79495L1.58046 4.356ZM1.06966 3.26775L4.60966 1.27575L4.11926 0.404252L0.579256 2.39625L1.06966 3.26775ZM4.36446 1.34H5.84046V0.34H4.36446V1.34ZM5.34046 0.84V9H6.34046V0.84H5.34046ZM5.84046 8.5H3.82446V9.5H5.84046V8.5ZM4.32446 9V3.132H3.32446V9H4.32446ZM3.58503 2.69305L1.34103 3.91705L1.81988 4.79495L4.06388 3.57095L3.58503 2.69305ZM2.02837 4.1338L1.27237 2.6098L0.37654 3.05419L1.13254 4.57819L2.02837 4.1338ZM12.768 3.072L13.2441 3.22477L13.3791 2.8041L12.9783 2.61834L12.768 3.072ZM12.252 4.68L12.0177 5.12167L12.5455 5.40176L12.7281 4.83277L12.252 4.68ZM10.14 5.016L10.2698 4.53314L10.2689 4.53288L10.14 5.016ZM11.256 5.316L11.388 4.83373L11.3858 4.83314L11.256 5.316ZM7.66802 8.58L7.18146 8.46486L7.09028 8.85015L7.44441 9.02721L7.66802 8.58ZM8.04002 7.008L8.27099 6.56454L7.70136 6.26786L7.55346 6.89286L8.04002 7.008ZM10.104 6.804L9.9705 7.28585L9.97246 7.28638L10.104 6.804ZM9.10802 6.528L8.96692 7.00774L8.9745 7.00984L9.10802 6.528ZM12.2919 2.91923L11.7759 4.52723L12.7281 4.83277L13.2441 3.22477L12.2919 2.91923ZM12.4864 4.23833C11.8393 3.89499 11.0634 3.676 10.38 3.676V4.676C10.8726 4.676 11.4887 4.84101 12.0177 5.12167L12.4864 4.23833ZM10.38 3.676C10.1396 3.676 9.86418 3.70948 9.62933 3.82435C9.37391 3.94928 9.12402 4.20141 9.12402 4.596H10.124C10.124 4.62456 10.1135 4.66451 10.0886 4.69857C10.0679 4.72699 10.0523 4.73066 10.0687 4.72265C10.0851 4.71461 10.1196 4.70193 10.177 4.69178C10.233 4.68185 10.3015 4.676 10.38 4.676V3.676ZM9.12402 4.596C9.12402 4.71012 9.14267 4.83675 9.20143 4.96189C9.26147 5.08975 9.34975 5.18705 9.44702 5.26C9.61808 5.38829 9.83679 5.45261 10.0112 5.49912L10.2689 4.53288C10.1853 4.51061 10.1279 4.49271 10.0872 4.47702C10.0456 4.46101 10.0386 4.45371 10.047 4.46C10.0603 4.46995 10.0871 4.49525 10.1066 4.53686C10.1249 4.57575 10.124 4.60188 10.124 4.596H9.12402ZM10.0102 5.49886L11.1262 5.79886L11.3858 4.83314L10.2698 4.53314L10.0102 5.49886ZM11.124 5.79826C11.6301 5.93678 11.8946 6.12324 12.0391 6.30874C12.1813 6.49136 12.256 6.73555 12.256 7.092H13.256C13.256 6.59645 13.1507 6.10864 12.828 5.69426C12.5075 5.28276 12.0219 5.00722 11.388 4.83374L11.124 5.79826ZM12.256 7.092C12.256 7.55363 12.0794 7.91341 11.7578 8.16917C11.4242 8.43447 10.8916 8.62 10.14 8.62V9.62C11.0444 9.62 11.8198 9.39753 12.3802 8.95183C12.9527 8.49659 13.256 7.84237 13.256 7.092H12.256ZM10.14 8.62C9.1224 8.62 8.31048 8.34221 7.89163 8.13279L7.44441 9.02721C7.98556 9.29779 8.94964 9.62 10.14 9.62V8.62ZM8.15458 8.69514L8.52658 7.12314L7.55346 6.89286L7.18146 8.46486L8.15458 8.69514ZM7.80905 7.45146C8.40485 7.76177 9.2367 8.096 9.99602 8.096V7.096C9.48334 7.096 8.82719 6.85423 8.27099 6.56454L7.80905 7.45146ZM9.99602 8.096C10.2458 8.096 10.5223 8.07335 10.752 7.96795C10.8752 7.91141 11.0039 7.82255 11.1003 7.68367C11.1993 7.5411 11.24 7.38059 11.24 7.224H10.24C10.24 7.20541 10.2462 7.1604 10.2788 7.11346C10.3089 7.0702 10.3393 7.05709 10.335 7.05905C10.3286 7.062 10.3003 7.07281 10.2379 7.08188C10.1771 7.0907 10.0974 7.096 9.99602 7.096V8.096ZM11.24 7.224C11.24 6.87574 11.0153 6.66232 10.8286 6.54977C10.6543 6.44469 10.4355 6.37614 10.2356 6.32162L9.97246 7.28638C10.0647 7.31153 10.1392 7.3344 10.1992 7.35627C10.2605 7.37859 10.2951 7.39582 10.3124 7.40623C10.3304 7.41709 10.3122 7.41019 10.2883 7.37651C10.259 7.33516 10.24 7.27972 10.24 7.224H11.24ZM10.2375 6.32216L9.24154 6.04616L8.9745 7.00984L9.9705 7.28584L10.2375 6.32216ZM9.2491 6.04832C8.79416 5.91451 8.51275 5.73831 8.34382 5.5404C8.18236 5.35123 8.08402 5.09595 8.08402 4.716H7.08402C7.08402 5.28405 7.23768 5.78477 7.58321 6.1896C7.92129 6.58569 8.40187 6.84149 8.96694 7.00768L9.2491 6.04832ZM8.08402 4.716C8.08402 4.25415 8.27906 3.87547 8.64902 3.598C9.0313 3.31129 9.62713 3.116 10.428 3.116V2.116C9.47691 2.116 8.65074 2.34671 8.04902 2.798C7.43498 3.25853 7.08402 3.92985 7.08402 4.716H8.08402ZM10.428 3.116C11.1948 3.116 12.1413 3.33265 12.5578 3.52566L12.9783 2.61834C12.4107 2.35535 11.3172 2.116 10.428 2.116V3.116ZM15.0524 1.128V0.628H14.6252L14.5585 1.05002L15.0524 1.128ZM16.7564 1.128H17.2564V0.628H16.7564V1.128ZM16.7564 2.724H16.2564V3.224H16.7564V2.724ZM18.9284 2.724H19.4284V2.224H18.9284V2.724ZM18.9284 4.284V4.784H19.4284V4.284H18.9284ZM16.7564 4.284V3.784H16.2564V4.284H16.7564ZM18.6764 6.948L19.1409 6.76293L18.9058 6.17281L18.3874 6.53999L18.6764 6.948ZM19.2884 8.484L19.5505 8.90983L19.9085 8.68949L19.7529 8.29893L19.2884 8.484ZM14.7404 4.284H15.2404V3.784H14.7404V4.284ZM13.6724 4.284H13.1724V4.784H13.6724V4.284ZM13.6724 2.724V2.224H13.1724V2.724H13.6724ZM14.8004 2.724V3.224H15.2277L15.2943 2.80198L14.8004 2.724ZM15.0524 1.628H16.7564V0.628H15.0524V1.628ZM16.2564 1.128V2.724H17.2564V1.128H16.2564ZM16.7564 3.224H18.9284V2.224H16.7564V3.224ZM18.4284 2.724V4.284H19.4284V2.724H18.4284ZM18.9284 3.784H16.7564V4.784H18.9284V3.784ZM16.2564 4.284V6.468H17.2564V4.284H16.2564ZM16.2564 6.468C16.2564 6.8419 16.3319 7.2084 16.5705 7.48382C16.822 7.77425 17.1696 7.868 17.5004 7.868V6.868C17.3512 6.868 17.3269 6.82975 17.3264 6.82918C17.3129 6.8136 17.2564 6.7301 17.2564 6.468H16.2564ZM17.5004 7.868C18.0759 7.868 18.6328 7.59166 18.9654 7.35601L18.3874 6.53999C18.1441 6.71234 17.7889 6.868 17.5004 6.868V7.868ZM18.2119 7.13307L18.8239 8.66907L19.7529 8.29893L19.1409 6.76293L18.2119 7.13307ZM19.0264 8.05817C18.4871 8.39004 17.7511 8.62 16.8524 8.62V9.62C17.9218 9.62 18.8418 9.34596 19.5505 8.90983L19.0264 8.05817ZM16.8524 8.62C16.3004 8.62 15.9179 8.4555 15.6699 8.19219C15.4181 7.92484 15.2404 7.48705 15.2404 6.816H14.2404C14.2404 7.65695 14.4648 8.37116 14.9419 8.87781C15.4229 9.3885 16.0964 9.62 16.8524 9.62V8.62ZM15.2404 6.816V4.284H14.2404V6.816H15.2404ZM14.7404 3.784H13.6724V4.784H14.7404V3.784ZM14.1724 4.284V2.724H13.1724V4.284H14.1724ZM13.6724 3.224H14.8004V2.224H13.6724V3.224ZM15.2943 2.80198L15.5463 1.20598L14.5585 1.05002L14.3065 2.64602L15.2943 2.80198Z'
      fill='#3E3F00'
      mask='url(#path-1-outside-1_846_1650)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_846_1650'
        x1='10.1685'
        y1='1'
        x2='10.1685'
        y2='9'
        gradientUnits='userSpaceOnUse'
      >
        <stop offset='0.59' stop-color='#FFC400' />
        <stop offset='0.68' stop-color='#997500' />
        <stop offset='1' stop-color='#FFC400' />
      </linearGradient>
    </defs>
  </svg>
)
const Two = (
  <svg
    width='25'
    height='10'
    viewBox='0 0 25 10'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <mask
      id='path-1-outside-1_846_1658'
      maskUnits='userSpaceOnUse'
      x='0.168457'
      y='-1'
      width='26'
      height='11'
      fill='black'
    >
      <rect fill='white' x='0.168457' y='-1' width='26' height='11' />
      <path d='M7.45246 7.248V9H1.06846V7.5L4.14046 5.208C4.90846 4.632 5.19646 4.152 5.19646 3.588C5.19646 3.024 4.81246 2.556 4.06846 2.556C3.08446 2.556 2.65246 3.168 2.65246 4.044L0.816457 3.696C0.864457 1.884 2.05246 0.719999 4.10446 0.719999C6.24046 0.719999 7.26046 1.98 7.26046 3.408C7.26046 4.416 6.92446 5.256 5.60446 6.18L4.05646 7.248H7.45246ZM9.1198 2.724H11.1358V3.42C11.5918 2.94 12.2398 2.616 13.0438 2.616C14.6638 2.616 15.3478 3.852 15.3478 5.388V6.852C15.3478 7.164 15.4198 7.428 15.7318 7.428C15.9238 7.428 16.1398 7.356 16.3438 7.26L16.4038 8.856C16.0678 9 15.5278 9.12 14.9158 9.12C13.9078 9.12 13.3318 8.628 13.3318 7.344V5.724C13.3318 4.92 12.9598 4.476 12.2758 4.476C11.7118 4.476 11.3158 4.896 11.1358 5.256V9H9.1198V2.724ZM21.8533 0.48H23.8693V6.852C23.8693 7.176 23.9413 7.428 24.2533 7.428C24.4213 7.428 24.6253 7.356 24.8533 7.26L24.9253 8.856C24.6133 8.988 24.0133 9.12 23.4253 9.12C22.7173 9.12 22.2253 8.88 21.9973 8.268C21.6013 8.772 20.9773 9.12 20.0653 9.12C18.3613 9.12 17.1733 7.752 17.1733 5.856C17.1733 3.96 18.3613 2.616 20.0653 2.616C20.8213 2.616 21.4333 2.88 21.8533 3.264V0.48ZM19.2253 5.856C19.2253 6.78 19.7773 7.368 20.6533 7.368C21.1093 7.368 21.5893 7.164 21.8533 6.924V4.824C21.5533 4.548 21.0973 4.368 20.6533 4.368C19.7893 4.368 19.2253 4.944 19.2253 5.856Z' />
    </mask>
    <path
      d='M7.45246 7.248V9H1.06846V7.5L4.14046 5.208C4.90846 4.632 5.19646 4.152 5.19646 3.588C5.19646 3.024 4.81246 2.556 4.06846 2.556C3.08446 2.556 2.65246 3.168 2.65246 4.044L0.816457 3.696C0.864457 1.884 2.05246 0.719999 4.10446 0.719999C6.24046 0.719999 7.26046 1.98 7.26046 3.408C7.26046 4.416 6.92446 5.256 5.60446 6.18L4.05646 7.248H7.45246ZM9.1198 2.724H11.1358V3.42C11.5918 2.94 12.2398 2.616 13.0438 2.616C14.6638 2.616 15.3478 3.852 15.3478 5.388V6.852C15.3478 7.164 15.4198 7.428 15.7318 7.428C15.9238 7.428 16.1398 7.356 16.3438 7.26L16.4038 8.856C16.0678 9 15.5278 9.12 14.9158 9.12C13.9078 9.12 13.3318 8.628 13.3318 7.344V5.724C13.3318 4.92 12.9598 4.476 12.2758 4.476C11.7118 4.476 11.3158 4.896 11.1358 5.256V9H9.1198V2.724ZM21.8533 0.48H23.8693V6.852C23.8693 7.176 23.9413 7.428 24.2533 7.428C24.4213 7.428 24.6253 7.356 24.8533 7.26L24.9253 8.856C24.6133 8.988 24.0133 9.12 23.4253 9.12C22.7173 9.12 22.2253 8.88 21.9973 8.268C21.6013 8.772 20.9773 9.12 20.0653 9.12C18.3613 9.12 17.1733 7.752 17.1733 5.856C17.1733 3.96 18.3613 2.616 20.0653 2.616C20.8213 2.616 21.4333 2.88 21.8533 3.264V0.48ZM19.2253 5.856C19.2253 6.78 19.7773 7.368 20.6533 7.368C21.1093 7.368 21.5893 7.164 21.8533 6.924V4.824C21.5533 4.548 21.0973 4.368 20.6533 4.368C19.7893 4.368 19.2253 4.944 19.2253 5.856Z'
      fill='url(#paint0_linear_846_1658)'
    />
    <path
      d='M7.45246 7.248H7.95246V6.748H7.45246V7.248ZM7.45246 9V9.5H7.95246V9H7.45246ZM1.06846 9H0.568457V9.5H1.06846V9ZM1.06846 7.5L0.76946 7.09925L0.568457 7.24922V7.5H1.06846ZM4.14046 5.208L4.43946 5.60875L4.44046 5.608L4.14046 5.208ZM2.65246 4.044L2.55934 4.53525L3.15246 4.64767V4.044H2.65246ZM0.816457 3.696L0.316632 3.68276L0.305367 4.10803L0.723344 4.18725L0.816457 3.696ZM5.60446 6.18L5.8884 6.59156L5.89119 6.58962L5.60446 6.18ZM4.05646 7.248L3.77252 6.83644L2.45127 7.748H4.05646V7.248ZM6.95246 7.248V9H7.95246V7.248H6.95246ZM7.45246 8.5H1.06846V9.5H7.45246V8.5ZM1.56846 9V7.5H0.568457V9H1.56846ZM1.36745 7.90075L4.43945 5.60875L3.84146 4.80725L0.76946 7.09925L1.36745 7.90075ZM4.44046 5.608C5.26482 4.98973 5.69646 4.37968 5.69646 3.588H4.69646C4.69646 3.92432 4.5521 4.27427 3.84046 4.808L4.44046 5.608ZM5.69646 3.588C5.69646 3.1919 5.56004 2.79963 5.2636 2.50554C4.9651 2.20941 4.54823 2.056 4.06846 2.056V3.056C4.33268 3.056 4.47981 3.13659 4.55932 3.21546C4.64088 3.29637 4.69646 3.4201 4.69646 3.588H5.69646ZM4.06846 2.056C3.46846 2.056 2.9633 2.24664 2.6161 2.64119C2.2785 3.02482 2.15246 3.53112 2.15246 4.044H3.15246C3.15246 3.68087 3.24241 3.44318 3.36681 3.30181C3.48161 3.17136 3.68446 3.056 4.06846 3.056V2.056ZM2.74557 3.55275L0.90957 3.20475L0.723344 4.18725L2.55934 4.53525L2.74557 3.55275ZM1.31628 3.70924C1.33739 2.91235 1.60525 2.3055 2.04846 1.89556C2.49431 1.48318 3.17203 1.22 4.10446 1.22V0.219999C2.98488 0.219999 2.0426 0.538816 1.36945 1.16144C0.693659 1.7865 0.343523 2.66764 0.316632 3.68276L1.31628 3.70924ZM4.10446 1.22C5.07316 1.22 5.7296 1.50387 6.13996 1.89183C6.55057 2.28002 6.76046 2.81481 6.76046 3.408H7.76046C7.76046 2.57319 7.46035 1.76398 6.82695 1.16517C6.19332 0.566128 5.27175 0.219999 4.10446 0.219999V1.22ZM6.76046 3.408C6.76046 3.85224 6.68739 4.22545 6.48963 4.58404C6.28869 4.94838 5.93745 5.33658 5.31773 5.77038L5.89119 6.58962C6.59146 6.09942 7.06822 5.60562 7.36529 5.06696C7.66553 4.52255 7.76046 3.97176 7.76046 3.408H6.76046ZM5.32052 5.76845L3.77252 6.83644L4.3404 7.65955L5.8884 6.59155L5.32052 5.76845ZM4.05646 7.748H7.45246V6.748H4.05646V7.748ZM9.1198 2.724V2.224H8.6198V2.724H9.1198ZM11.1358 2.724H11.6358V2.224H11.1358V2.724ZM11.1358 3.42H10.6358V4.67227L11.4983 3.76437L11.1358 3.42ZM16.3438 7.26L16.8434 7.24122L16.815 6.48564L16.1309 6.80759L16.3438 7.26ZM16.4038 8.856L16.6008 9.31557L16.9163 9.18032L16.9034 8.83722L16.4038 8.856ZM11.1358 5.256L10.6886 5.03239L10.6358 5.13797V5.256H11.1358ZM11.1358 9V9.5H11.6358V9H11.1358ZM9.1198 9H8.6198V9.5H9.1198V9ZM9.1198 3.224H11.1358V2.224H9.1198V3.224ZM10.6358 2.724V3.42H11.6358V2.724H10.6358ZM11.4983 3.76437C11.865 3.37837 12.3841 3.116 13.0438 3.116V2.116C12.0955 2.116 11.3186 2.50163 10.7733 3.07562L11.4983 3.76437ZM13.0438 3.116C13.7098 3.116 14.1313 3.36116 14.4024 3.72485C14.6897 4.11015 14.8478 4.68418 14.8478 5.388H15.8478C15.8478 4.55582 15.6639 3.74385 15.2042 3.12715C14.7283 2.48884 13.9978 2.116 13.0438 2.116V3.116ZM14.8478 5.388V6.852H15.8478V5.388H14.8478ZM14.8478 6.852C14.8478 7.02691 14.8643 7.27627 14.9834 7.49555C15.0482 7.61494 15.1462 7.73063 15.2876 7.81342C15.4283 7.89573 15.582 7.928 15.7318 7.928V6.928C15.7256 6.928 15.7533 6.92727 15.7927 6.95033C15.8329 6.97387 15.8544 7.00406 15.8622 7.01845C15.8678 7.0288 15.8626 7.02351 15.8569 6.99011C15.8514 6.95781 15.8478 6.91254 15.8478 6.852H14.8478ZM15.7318 7.928C16.034 7.928 16.3299 7.81915 16.5567 7.71241L16.1309 6.80759C15.9497 6.89285 15.8136 6.928 15.7318 6.928V7.928ZM15.8442 7.27878L15.9042 8.87478L16.9034 8.83722L16.8434 7.24122L15.8442 7.27878ZM16.2068 8.39643C15.9423 8.5098 15.4709 8.62 14.9158 8.62V9.62C15.5847 9.62 16.1933 9.4902 16.6008 9.31557L16.2068 8.39643ZM14.9158 8.62C14.4863 8.62 14.2477 8.51552 14.1079 8.36867C13.9628 8.21637 13.8318 7.92246 13.8318 7.344H12.8318C12.8318 8.04954 12.9888 8.64363 13.3837 9.05833C13.7839 9.47848 14.3373 9.62 14.9158 9.62V8.62ZM13.8318 7.344V5.724H12.8318V7.344H13.8318ZM13.8318 5.724C13.8318 5.25337 13.7238 4.8094 13.4424 4.47601C13.1495 4.12899 12.7321 3.976 12.2758 3.976V4.976C12.5035 4.976 12.6141 5.04501 12.6782 5.12099C12.7538 5.2106 12.8318 5.39063 12.8318 5.724H13.8318ZM12.2758 3.976C11.4443 3.976 10.9115 4.5865 10.6886 5.03239L11.583 5.47961C11.7201 5.2055 11.9793 4.976 12.2758 4.976V3.976ZM10.6358 5.256V9H11.6358V5.256H10.6358ZM11.1358 8.5H9.1198V9.5H11.1358V8.5ZM9.6198 9V2.724H8.6198V9H9.6198ZM21.8533 0.48V-0.0200005H21.3533V0.48H21.8533ZM23.8693 0.48H24.3693V-0.0200005H23.8693V0.48ZM24.8533 7.26L25.3528 7.23747L25.3205 6.52079L24.6593 6.79918L24.8533 7.26ZM24.9253 8.856L25.1201 9.31648L25.4405 9.18095L25.4248 8.83347L24.9253 8.856ZM21.9973 8.268L22.4658 8.09345L22.1547 7.25835L21.6041 7.95909L21.9973 8.268ZM21.8533 3.264L21.5159 3.63301L22.3533 4.39862V3.264H21.8533ZM21.8533 6.924L22.1896 7.29397L22.3533 7.14518V6.924H21.8533ZM21.8533 4.824H22.3533V4.60459L22.1918 4.45603L21.8533 4.824ZM21.8533 0.98H23.8693V-0.0200005H21.8533V0.98ZM23.3693 0.48V6.852H24.3693V0.48H23.3693ZM23.3693 6.852C23.3693 7.03003 23.3851 7.2825 23.5063 7.5027C23.5729 7.62351 23.6728 7.73793 23.8152 7.81857C23.9556 7.89816 24.1074 7.928 24.2533 7.928V6.928C24.2432 6.928 24.269 6.92634 24.3082 6.94855C24.3493 6.97182 24.3727 7.00299 24.3823 7.0203C24.3892 7.03294 24.3842 7.02952 24.3785 6.99584C24.373 6.9632 24.3693 6.91642 24.3693 6.852H23.3693ZM24.2533 7.928C24.5357 7.928 24.8276 7.81335 25.0473 7.72082L24.6593 6.79918C24.423 6.89865 24.3069 6.928 24.2533 6.928V7.928ZM24.3538 7.28253L24.4258 8.87853L25.4248 8.83347L25.3528 7.23747L24.3538 7.28253ZM24.7305 8.39552C24.4884 8.49792 23.956 8.62 23.4253 8.62V9.62C24.0706 9.62 24.7382 9.47808 25.1201 9.31648L24.7305 8.39552ZM23.4253 8.62C23.1227 8.62 22.9175 8.56795 22.7785 8.48888C22.6508 8.41625 22.5432 8.30108 22.4658 8.09345L21.5288 8.44255C21.6794 8.84692 21.9318 9.15775 22.2841 9.35812C22.6251 9.55205 23.0199 9.62 23.4253 9.62V8.62ZM21.6041 7.95909C21.309 8.3347 20.834 8.62 20.0653 8.62V9.62C21.1206 9.62 21.8936 9.2093 22.3905 8.57691L21.6041 7.95909ZM20.0653 8.62C18.6952 8.62 17.6733 7.53736 17.6733 5.856H16.6733C16.6733 7.96664 18.0274 9.62 20.0653 9.62V8.62ZM17.6733 5.856C17.6733 5.01166 17.937 4.32566 18.3538 3.85742C18.7668 3.39342 19.3528 3.116 20.0653 3.116V2.116C19.0738 2.116 18.2138 2.51058 17.6068 3.19258C17.0036 3.87034 16.6733 4.80434 16.6733 5.856H17.6733ZM20.0653 3.116C20.7016 3.116 21.1918 3.33671 21.5159 3.63301L22.1907 2.89499C21.6748 2.42329 20.941 2.116 20.0653 2.116V3.116ZM22.3533 3.264V0.48H21.3533V3.264H22.3533ZM18.7253 5.856C18.7253 6.41687 18.8942 6.92729 19.247 7.30151C19.6029 7.67897 20.0993 7.868 20.6533 7.868V6.868C20.3313 6.868 20.1137 6.76303 19.9746 6.61549C19.8324 6.46471 19.7253 6.21913 19.7253 5.856H18.7253ZM20.6533 7.868C21.2331 7.868 21.8332 7.61801 22.1896 7.29397L21.517 6.55403C21.3454 6.70999 20.9855 6.868 20.6533 6.868V7.868ZM22.3533 6.924V4.824H21.3533V6.924H22.3533ZM22.1918 4.45603C21.7968 4.09263 21.219 3.868 20.6533 3.868V4.868C20.9756 4.868 21.3098 5.00337 21.5148 5.19197L22.1918 4.45603ZM20.6533 3.868C20.107 3.868 19.6126 4.05279 19.2555 4.42278C18.9001 4.79109 18.7253 5.29637 18.7253 5.856H19.7253C19.7253 5.50363 19.8325 5.26491 19.9751 5.11722C20.116 4.97121 20.3357 4.868 20.6533 4.868V3.868Z'
      fill='#383838'
      mask='url(#path-1-outside-1_846_1658)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_846_1658'
        x1='13.1685'
        y1='1'
        x2='13.1685'
        y2='9'
        gradientUnits='userSpaceOnUse'
      >
        <stop offset='0.701' stop-color='#E1E1E1' />
        <stop offset='0.736' stop-color='#656565' />
        <stop offset='1' stop-color='#E1E1E1' />
      </linearGradient>
    </defs>
  </svg>
)

const Three = (
  <svg
    width='22'
    height='10'
    viewBox='0 0 22 10'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <mask
      id='path-1-outside-1_846_1666'
      maskUnits='userSpaceOnUse'
      x='-0.331543'
      y='-1'
      width='23'
      height='11'
      fill='black'
    >
      <rect fill='white' x='-0.331543' y='-1' width='23' height='11' />
      <path d='M1.37246 5.64V4.008H3.16046C3.95246 4.008 4.34846 3.696 4.34846 3.216C4.34846 2.676 3.80846 2.424 3.04046 2.424C2.11646 2.424 1.09646 2.748 0.736457 2.892L0.280457 1.188C0.796457 0.995999 1.99646 0.719999 3.02846 0.719999C5.26046 0.719999 6.38846 1.524 6.38846 3.012C6.38846 3.804 5.92046 4.512 5.10446 4.74C6.16046 5.064 6.67646 5.676 6.67646 6.756C6.67646 8.184 5.46446 9.12 3.02846 9.12C1.86446 9.12 0.736457 8.772 0.316457 8.64L0.796457 6.972C1.20446 7.14 2.11646 7.416 3.02846 7.416C3.98846 7.416 4.60046 7.044 4.60046 6.444C4.60046 5.868 4.10846 5.64 3.16046 5.64H1.37246ZM8.79539 9V4.884C8.79539 4.56 8.73539 4.308 8.41139 4.308C8.15939 4.308 7.85939 4.428 7.79939 4.464L7.73939 2.88C7.78739 2.82 8.48339 2.616 9.22739 2.616C9.97139 2.616 10.4754 2.892 10.6914 3.54C10.9914 2.976 11.4234 2.616 12.2154 2.616C12.8034 2.616 13.2834 2.832 13.3314 2.916L13.0794 4.752C13.0194 4.668 12.4794 4.476 11.8794 4.476C11.1354 4.476 10.8714 5.076 10.8114 5.724V9H8.79539ZM18.7166 0.48H20.7326V6.852C20.7326 7.176 20.8046 7.428 21.1166 7.428C21.2846 7.428 21.4886 7.356 21.7166 7.26L21.7886 8.856C21.4766 8.988 20.8766 9.12 20.2886 9.12C19.5806 9.12 19.0886 8.88 18.8606 8.268C18.4646 8.772 17.8406 9.12 16.9286 9.12C15.2246 9.12 14.0366 7.752 14.0366 5.856C14.0366 3.96 15.2246 2.616 16.9286 2.616C17.6846 2.616 18.2966 2.88 18.7166 3.264V0.48ZM16.0886 5.856C16.0886 6.78 16.6406 7.368 17.5166 7.368C17.9726 7.368 18.4526 7.164 18.7166 6.924V4.824C18.4166 4.548 17.9606 4.368 17.5166 4.368C16.6526 4.368 16.0886 4.944 16.0886 5.856Z' />
    </mask>
    <path
      d='M1.37246 5.64V4.008H3.16046C3.95246 4.008 4.34846 3.696 4.34846 3.216C4.34846 2.676 3.80846 2.424 3.04046 2.424C2.11646 2.424 1.09646 2.748 0.736457 2.892L0.280457 1.188C0.796457 0.995999 1.99646 0.719999 3.02846 0.719999C5.26046 0.719999 6.38846 1.524 6.38846 3.012C6.38846 3.804 5.92046 4.512 5.10446 4.74C6.16046 5.064 6.67646 5.676 6.67646 6.756C6.67646 8.184 5.46446 9.12 3.02846 9.12C1.86446 9.12 0.736457 8.772 0.316457 8.64L0.796457 6.972C1.20446 7.14 2.11646 7.416 3.02846 7.416C3.98846 7.416 4.60046 7.044 4.60046 6.444C4.60046 5.868 4.10846 5.64 3.16046 5.64H1.37246ZM8.79539 9V4.884C8.79539 4.56 8.73539 4.308 8.41139 4.308C8.15939 4.308 7.85939 4.428 7.79939 4.464L7.73939 2.88C7.78739 2.82 8.48339 2.616 9.22739 2.616C9.97139 2.616 10.4754 2.892 10.6914 3.54C10.9914 2.976 11.4234 2.616 12.2154 2.616C12.8034 2.616 13.2834 2.832 13.3314 2.916L13.0794 4.752C13.0194 4.668 12.4794 4.476 11.8794 4.476C11.1354 4.476 10.8714 5.076 10.8114 5.724V9H8.79539ZM18.7166 0.48H20.7326V6.852C20.7326 7.176 20.8046 7.428 21.1166 7.428C21.2846 7.428 21.4886 7.356 21.7166 7.26L21.7886 8.856C21.4766 8.988 20.8766 9.12 20.2886 9.12C19.5806 9.12 19.0886 8.88 18.8606 8.268C18.4646 8.772 17.8406 9.12 16.9286 9.12C15.2246 9.12 14.0366 7.752 14.0366 5.856C14.0366 3.96 15.2246 2.616 16.9286 2.616C17.6846 2.616 18.2966 2.88 18.7166 3.264V0.48ZM16.0886 5.856C16.0886 6.78 16.6406 7.368 17.5166 7.368C17.9726 7.368 18.4526 7.164 18.7166 6.924V4.824C18.4166 4.548 17.9606 4.368 17.5166 4.368C16.6526 4.368 16.0886 4.944 16.0886 5.856Z'
      fill='url(#paint0_linear_846_1666)'
    />
    <path
      d='M1.37246 5.64H0.872457V6.14H1.37246V5.64ZM1.37246 4.008V3.508H0.872457V4.008H1.37246ZM0.736457 2.892L0.253453 3.02125L0.399086 3.56547L0.922153 3.35624L0.736457 2.892ZM0.280457 1.188L0.10609 0.719389L-0.320102 0.877972L-0.202547 1.31725L0.280457 1.188ZM5.10446 4.74L4.9699 4.25844L3.32673 4.71757L4.9578 5.21801L5.10446 4.74ZM0.316457 8.64L-0.164043 8.50173L-0.298995 8.97068L0.166544 9.117L0.316457 8.64ZM0.796457 6.972L0.986832 6.50966L0.470406 6.29701L0.315957 6.83373L0.796457 6.972ZM1.87246 5.64V4.008H0.872457V5.64H1.87246ZM1.37246 4.508H3.16046V3.508H1.37246V4.508ZM3.16046 4.508C3.61134 4.508 4.02358 4.42089 4.33735 4.2022C4.67449 3.96723 4.84846 3.61334 4.84846 3.216H3.84846C3.84846 3.29866 3.82443 3.34077 3.76556 3.3818C3.68333 3.43911 3.50158 3.508 3.16046 3.508V4.508ZM4.84846 3.216C4.84846 2.74081 4.59344 2.38839 4.22839 2.18217C3.89156 1.99187 3.46654 1.924 3.04046 1.924V2.924C3.38237 2.924 3.61136 2.98213 3.73652 3.05283C3.83347 3.1076 3.84846 3.15119 3.84846 3.216H4.84846ZM3.04046 1.924C2.02658 1.924 0.939196 2.27239 0.550761 2.42776L0.922153 3.35624C1.25372 3.22361 2.20634 2.924 3.04046 2.924V1.924ZM1.21946 2.76275L0.763461 1.05875L-0.202547 1.31725L0.253453 3.02125L1.21946 2.76275ZM0.454824 1.65661C0.920328 1.4834 2.06138 1.22 3.02846 1.22V0.219999C1.93153 0.219999 0.672586 0.508599 0.10609 0.719389L0.454824 1.65661ZM3.02846 1.22C4.0983 1.22 4.82019 1.41511 5.26154 1.71957C5.67237 2.00298 5.88846 2.41344 5.88846 3.012H6.88846C6.88846 2.12256 6.54054 1.38702 5.82938 0.896429C5.14873 0.426891 4.19061 0.219999 3.02846 0.219999V1.22ZM5.88846 3.012C5.88846 3.60152 5.54952 4.09649 4.9699 4.25844L5.23901 5.22156C6.29139 4.92751 6.88846 4.00648 6.88846 3.012H5.88846ZM4.9578 5.21801C5.42667 5.36187 5.71746 5.55211 5.89498 5.77554C6.06847 5.9939 6.17646 6.29773 6.17646 6.756H7.17646C7.17646 6.13427 7.02644 5.5921 6.67794 5.15346C6.33345 4.71989 5.83824 4.44213 5.25112 4.26199L4.9578 5.21801ZM6.17646 6.756C6.17646 7.30673 5.95354 7.74176 5.48508 8.05985C4.99236 8.39441 4.19346 8.62 3.02846 8.62V9.62C4.29945 9.62 5.32455 9.37759 6.04683 8.88715C6.79337 8.38024 7.17646 7.63327 7.17646 6.756H6.17646ZM3.02846 8.62C1.94789 8.62 0.884094 8.29429 0.46637 8.163L0.166544 9.117C0.58882 9.24971 1.78102 9.62 3.02846 9.62V8.62ZM0.796957 8.77827L1.27696 7.11027L0.315957 6.83373L-0.164043 8.50173L0.796957 8.77827ZM0.606082 7.43434C1.05432 7.61891 2.03241 7.916 3.02846 7.916V6.916C2.2005 6.916 1.35459 6.66109 0.986832 6.50966L0.606082 7.43434ZM3.02846 7.916C3.56351 7.916 4.05794 7.81379 4.43651 7.58083C4.83327 7.33667 5.10046 6.94417 5.10046 6.444H4.10046C4.10046 6.54383 4.06164 6.63733 3.91241 6.72917C3.74497 6.83221 3.4534 6.916 3.02846 6.916V7.916ZM5.10046 6.444C5.10046 6.2273 5.05352 6.01301 4.93842 5.81963C4.82311 5.62592 4.6581 5.48463 4.47154 5.38497C4.11784 5.19604 3.65509 5.14 3.16046 5.14V6.14C3.61383 6.14 3.87108 6.19796 4.00038 6.26702C4.05531 6.29637 4.07256 6.32008 4.07912 6.33112C4.08589 6.34249 4.10046 6.3727 4.10046 6.444H5.10046ZM3.16046 5.14H1.37246V6.14H3.16046V5.14ZM8.79539 9H8.29539V9.5H8.79539V9ZM7.79939 4.464L7.29975 4.48293L7.33175 5.32768L8.05664 4.89275L7.79939 4.464ZM7.73939 2.88L7.34896 2.56765L7.23271 2.71297L7.23975 2.89893L7.73939 2.88ZM10.6914 3.54L10.2171 3.69811L10.5856 4.80366L11.1328 3.77481L10.6914 3.54ZM13.3314 2.916L13.8268 2.98399L13.8499 2.81555L13.7655 2.66793L13.3314 2.916ZM13.0794 4.752L12.6725 5.04262L13.4037 6.06626L13.5748 4.81999L13.0794 4.752ZM10.8114 5.724L10.3135 5.6779L10.3114 5.7009V5.724H10.8114ZM10.8114 9V9.5H11.3114V9H10.8114ZM9.29539 9V4.884H8.29539V9H9.29539ZM9.29539 4.884C9.29539 4.71506 9.28455 4.46015 9.16554 4.23821C9.09919 4.11448 8.99793 3.99776 8.85253 3.91627C8.71025 3.83652 8.55696 3.808 8.41139 3.808V4.808C8.42782 4.808 8.40354 4.81098 8.36363 4.78861C8.34427 4.77775 8.3266 4.76382 8.31187 4.74824C8.2975 4.73303 8.28884 4.71935 8.28425 4.71079C8.27643 4.69621 8.28138 4.69843 8.28682 4.73364C8.29208 4.76773 8.29539 4.81616 8.29539 4.884H9.29539ZM8.41139 3.808C8.21595 3.808 8.02493 3.85267 7.88857 3.89393C7.76024 3.93277 7.62442 3.98589 7.54215 4.03525L8.05664 4.89275C8.0368 4.90465 8.03707 4.90218 8.0717 4.88823C8.09872 4.87735 8.13572 4.86393 8.17822 4.85106C8.26986 4.82333 8.35484 4.808 8.41139 4.808V3.808ZM8.29904 4.44507L8.23904 2.86107L7.23975 2.89893L7.29975 4.48293L8.29904 4.44507ZM8.12983 3.19235C8.07584 3.25983 8.01874 3.29248 8.01469 3.29493C7.99949 3.30412 7.9903 3.30806 7.99231 3.30715C7.99551 3.3057 8.01152 3.29903 8.04403 3.28831C8.10715 3.26751 8.2041 3.24077 8.32716 3.2144C8.57367 3.16158 8.8979 3.116 9.2274 3.116V2.116C8.81289 2.116 8.41712 2.17242 8.11763 2.2366C7.96769 2.26873 7.83514 2.30424 7.731 2.33857C7.67989 2.35541 7.62784 2.37451 7.58088 2.39571C7.55782 2.40612 7.52802 2.42059 7.49699 2.43937C7.47711 2.45139 7.40895 2.49267 7.34896 2.56765L8.12983 3.19235ZM9.2274 3.116C9.54195 3.116 9.75442 3.1749 9.89709 3.2619C10.03 3.34292 10.1416 3.47175 10.2171 3.69811L11.1657 3.38189C11.0252 2.96025 10.7768 2.62708 10.4177 2.4081C10.0684 2.1951 9.65684 2.116 9.2274 2.116V3.116ZM11.1328 3.77481C11.2574 3.54055 11.3903 3.38492 11.5433 3.28449C11.6923 3.18664 11.8988 3.116 12.2154 3.116V2.116C11.74 2.116 11.3345 2.22536 10.9945 2.44851C10.6585 2.66908 10.4254 2.97545 10.25 3.30519L11.1328 3.77481ZM12.2154 3.116C12.4506 3.116 12.6657 3.15968 12.8221 3.21025C12.9005 3.23561 12.956 3.26006 12.9866 3.27618C13.0025 3.28452 13.0042 3.2867 12.9966 3.28108C12.9926 3.27816 12.9814 3.26963 12.9667 3.25515C12.9541 3.24272 12.9249 3.21237 12.8973 3.16407L13.7655 2.66793C13.7061 2.56398 13.6234 2.50088 13.591 2.4769C13.5456 2.44339 13.4974 2.41492 13.4523 2.3912C13.361 2.34319 13.2508 2.29789 13.1297 2.25875C12.8871 2.18032 12.5681 2.116 12.2154 2.116V3.116ZM12.836 2.84801L12.584 4.68401L13.5748 4.81999L13.8268 2.98399L12.836 2.84801ZM13.4863 4.46138C13.4177 4.36539 13.33 4.31027 13.2965 4.28974C13.2488 4.2605 13.1981 4.23574 13.1506 4.21504C13.0545 4.17315 12.9383 4.1337 12.8118 4.09966C12.5585 4.03154 12.2296 3.976 11.8794 3.976V4.976C12.1292 4.976 12.3703 5.01646 12.552 5.06534C12.643 5.0898 12.7106 5.11409 12.7513 5.13183C12.7722 5.14095 12.7782 5.14497 12.7739 5.14231C12.7716 5.14089 12.7614 5.13457 12.747 5.12286C12.7351 5.11312 12.7041 5.08682 12.6725 5.04262L13.4863 4.46138ZM11.8794 3.976C11.3482 3.976 10.9446 4.20203 10.6833 4.56464C10.442 4.89934 10.3473 5.31359 10.3135 5.6779L11.3093 5.7701C11.3355 5.48641 11.4028 5.27666 11.4945 5.14936C11.5661 5.04997 11.6666 4.976 11.8794 4.976V3.976ZM10.3114 5.724V9H11.3114V5.724H10.3114ZM10.8114 8.5H8.79539V9.5H10.8114V8.5ZM18.7166 0.48V-0.0200005H18.2166V0.48H18.7166ZM20.7326 0.48H21.2326V-0.0200005H20.7326V0.48ZM21.7166 7.26L22.2161 7.23747L22.1837 6.52079L21.5226 6.79918L21.7166 7.26ZM21.7886 8.856L21.9834 9.31648L22.3038 9.18095L22.2881 8.83347L21.7886 8.856ZM18.8606 8.268L19.3291 8.09345L19.018 7.25835L18.4674 7.95909L18.8606 8.268ZM18.7166 3.264L18.3792 3.63301L19.2166 4.39862V3.264H18.7166ZM18.7166 6.924L19.0529 7.29397L19.2166 7.14518V6.924H18.7166ZM18.7166 4.824H19.2166V4.60459L19.0551 4.45603L18.7166 4.824ZM18.7166 0.98H20.7326V-0.0200005H18.7166V0.98ZM20.2326 0.48V6.852H21.2326V0.48H20.2326ZM20.2326 6.852C20.2326 7.03003 20.2483 7.2825 20.3696 7.5027C20.4361 7.62351 20.5361 7.73793 20.6785 7.81857C20.8189 7.89816 20.9707 7.928 21.1166 7.928V6.928C21.1064 6.928 21.1323 6.92634 21.1715 6.94855C21.2125 6.97182 21.236 7.00299 21.2456 7.0203C21.2525 7.03294 21.2475 7.02952 21.2418 6.99584C21.2363 6.9632 21.2326 6.91642 21.2326 6.852H20.2326ZM21.1166 7.928C21.399 7.928 21.6908 7.81335 21.9106 7.72082L21.5226 6.79918C21.2863 6.89865 21.1701 6.928 21.1166 6.928V7.928ZM21.2171 7.28253L21.2891 8.87853L22.2881 8.83347L22.2161 7.23747L21.2171 7.28253ZM21.5938 8.39552C21.3517 8.49792 20.8193 8.62 20.2886 8.62V9.62C20.9339 9.62 21.6014 9.47808 21.9834 9.31648L21.5938 8.39552ZM20.2886 8.62C19.986 8.62 19.7808 8.56795 19.6418 8.48888C19.5141 8.41625 19.4065 8.30108 19.3291 8.09345L18.392 8.44255C18.5427 8.84692 18.7951 9.15775 19.1474 9.35812C19.4884 9.55205 19.8832 9.62 20.2886 9.62V8.62ZM18.4674 7.95909C18.1723 8.3347 17.6973 8.62 16.9286 8.62V9.62C17.9839 9.62 18.7569 9.2093 19.2537 8.57691L18.4674 7.95909ZM16.9286 8.62C15.5585 8.62 14.5366 7.53736 14.5366 5.856H13.5366C13.5366 7.96664 14.8907 9.62 16.9286 9.62V8.62ZM14.5366 5.856C14.5366 5.01166 14.8003 4.32566 15.2171 3.85742C15.6301 3.39342 16.216 3.116 16.9286 3.116V2.116C15.9371 2.116 15.0771 2.51058 14.4701 3.19258C13.8668 3.87034 13.5366 4.80434 13.5366 5.856H14.5366ZM16.9286 3.116C17.5649 3.116 18.0551 3.33671 18.3792 3.63301L19.054 2.89499C18.5381 2.42329 17.8043 2.116 16.9286 2.116V3.116ZM19.2166 3.264V0.48H18.2166V3.264H19.2166ZM15.5886 5.856C15.5886 6.41687 15.7575 6.92729 16.1103 7.30151C16.4662 7.67897 16.9626 7.868 17.5166 7.868V6.868C17.1946 6.868 16.977 6.76303 16.8379 6.61549C16.6957 6.46471 16.5886 6.21913 16.5886 5.856H15.5886ZM17.5166 7.868C18.0964 7.868 18.6965 7.61801 19.0529 7.29397L18.3802 6.55403C18.2087 6.70999 17.8488 6.868 17.5166 6.868V7.868ZM19.2166 6.924V4.824H18.2166V6.924H19.2166ZM19.0551 4.45603C18.6601 4.09263 18.0823 3.868 17.5166 3.868V4.868C17.8389 4.868 18.1731 5.00337 18.3781 5.19197L19.0551 4.45603ZM17.5166 3.868C16.9702 3.868 16.4759 4.05279 16.1188 4.42278C15.7633 4.79109 15.5886 5.29637 15.5886 5.856H16.5886C16.5886 5.50363 16.6958 5.26491 16.8384 5.11722C16.9793 4.97121 17.1989 4.868 17.5166 4.868V3.868Z'
      fill='#493200'
      mask='url(#path-1-outside-1_846_1666)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_846_1666'
        x1='11.1685'
        y1='1'
        x2='11.1685'
        y2='9'
        gradientUnits='userSpaceOnUse'
      >
        <stop offset='0.701' stop-color='#FF8F44' />
        <stop offset='0.741' stop-color='#673400' />
        <stop offset='1' stop-color='#FF8F44' />
      </linearGradient>
    </defs>
  </svg>
)
