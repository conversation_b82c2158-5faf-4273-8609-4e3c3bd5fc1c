@tailwind base;
@tailwind components;
@tailwind utilities;

/* Example of @font-face for Pluto fonts */
@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoLight.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoBold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoMedium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondExtraLight.otf') format('opentype');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoThin.otf') format('opentype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoExtraLight.otf') format('opentype');
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondBlack.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoRegular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoHeavy.otf') format('opentype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondLight.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondBold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondHeavy.otf') format('opentype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondRegular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoBlack.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondThin.otf') format('opentype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Pluto';
  src: url('./assets/fonts/PlutoCondMedium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
}

/* Previous CSS remains the same */
body {
  margin: 0;
  font-family: 'Pluto', sans-serif;
  color: white;
}
canvas {
  display: block;
}
button svg {
  pointer-events: none;
}
button span {
  pointer-events: none;
}
#ui {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  color: white;
  background-color: white;
  background: white;
  font-family: Arial, sans-serif;
  text-align: center;
}
#score {
  position: fixed;
  top: 20px;
  right: 20px;
  color: white;
  font-family: Arial, sans-serif;
}
#controls {
  position: fixed;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

#joystick {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  -webkit-user-select: none;
}

#joystick-knob {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 1);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: transform 0.1s ease;
}

#joystick::after {
  content: '';
  position: absolute;
  width: 70px;
  height: 70px;
  /* border: 1px dashed rgba(255, 255, 255, 0.3); */
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
#shootButton {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
}

#timer {
  position: fixed;
  bottom: 170px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  border: 2px dashed white;
  padding: 4px;
  font-size: 40px;
  border-radius: 12px;
  width: 80px;
  text-align: center;
}
#logo {
  position: fixed;
  width: 70px;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
}
#title {
  position: fixed;
  top: 120px;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
  font-size: 32px;
  text-align: center;
  color: white;
}
#level {
  position: fixed;
  top: 170px;
  left: 50%;
  padding: 8px 12px;
  background: blue;
  color: white;
  transform: translateX(-50%);
  font-size: 32px;
  width: max-content;
  border: 4px solid white;
  display: none;
}

.title-shadow {
  text-shadow: 0px 3px 4px #1d46b0;
  leading-trim: both;
  text-edge: cap;
  text-align: center;
  line-height: 27px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.fade-in {
  animation: fadeIn 1s ease-in-out;
}

.fade-out {
  animation: fadeOut 1s ease-in-out;
}

input[type='date']:invalid::-webkit-datetime-edit {
  color: transparent;
}
input[type='date']:focus::-webkit-datetime-edit {
  color: white !important;
}

input[type='date']::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

.open {
  transition: transform 0.3s ease-in-out;
  transform: translateY(0);
}

.closed {
  transition: transform 0.3s ease-in-out;
  transform: translateY(100%);
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

.animate-fade-in-out {
  animation: fadeInOut 3s ease-in-out infinite;
}


@media (max-height: 700px) {
  .popup-oreo-star {
    margin-bottom: 20vh;
  }
}

.responsive-leaderboard {
  height: 50vh;
}

@media (max-height: 700px) {
  .responsive-leaderboard {
    height: calc(100vh - 500px);
    margin-bottom: 5vh;
  }
}

@media (max-height: 900px) {
  .responsive-leaderboard {
    height: calc(100vh - 600px);
    margin-bottom: 10vh;
  }
}

.scrollbar-hide {
  -ms-overflow-style: none; /* untuk Internet Explorer dan Edge */
  scrollbar-width: none; /* untuk Firefox */
  touch-action: none; /* mencegah scroll dengan touch */
  pointer-events: none; /* mencegah interaksi touch */
  -webkit-overflow-scrolling: auto; /* nonaktifkan smooth scroll iOS */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* untuk Chrome, Safari dan Opera */
}

@media (max-width: 768px) {
  .scrollbar-hide {
    overflow: hidden; /* mencegah scroll */
    touch-action: none;
    pointer-events: none;
    -webkit-overflow-scrolling: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }
}







.getOreoName {
  margin-bottom: 240px;
}

.title-mt {
  margin-top: 80px;
}

@media (min-height: 700px) {
  .getOreoName {
    margin-bottom: 250px;
  }
  .title-mt {
    margin-top: 100px;
  }
}
@media (min-height: 750px) {
  .getOreoName {
    margin-bottom: 270px;
  }
  .title-mt {
    margin-top: 120px;
  }
}
@media (min-height: 800px) {
  .getOreoName {
    margin-bottom: 290px;
  }
  .title-mt {
    margin-top: 140px;
  }
}
@media (min-height: 850px) {
  .getOreoName {
    margin-bottom: 310px;
  }
  .title-mt {
    margin-top: 150px;
  }
}
