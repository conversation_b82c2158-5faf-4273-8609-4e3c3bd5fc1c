declare let React: any
const scanBtnEn = require('../../../assets/images/scan-btn-en.png')
// const scanBtnCn = require('../../../assets/images/scan-btn-cn.png');
const scanBtnId = require('../../../assets/images/scan-btn-id.png')
const scanBtnVn = require('../../../assets/images/scan-btn-vn.png')
const scanBtnTh = require('../../../assets/images/scan-btn-th.png')
const scanBtnChina = require('../../../assets/images/scan-btn-china.png')
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
interface Props {
  onClick: () => void;
  disabled?: boolean;
}

const ScanButton = ({onClick, disabled}: Props) => {
  const {country} = useParams()

  let scanBtn = scanBtnEn
  switch (country?.toLowerCase()) {
    case 'id':
      scanBtn = scanBtnId
      break
    case 'vn':
      scanBtn = scanBtnVn
      break
    case 'th':
      scanBtn = scanBtnTh
      break
    case 'hk':
    case 'tw':
      scanBtn = scanBtnChina
      break
    default:
      scanBtn = scanBtnEn
  }
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`w-[100px] flex items-center justify-center ${
        disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'opacity-100 cursor-pointer'
      }`}
    >
      <img src={scanBtn} alt='scan-cookie' className='w-[70px]' />
    </button>
  )
}

export default ScanButton
