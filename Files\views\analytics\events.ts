import {triggerConversion, triggerConversionTTQ, triggerConversionMeta} from './gtag'
import {
  generateDunkNowId,
  generatePlayLevelId,
  generatePlayBonusId,
  generateSubmitPointId,
} from '../utils/generateId'

const eventMap: Record<string, Record<string, string>> = {
  DunkNow: {
    DunkNow: 'DC-9224723/inter0/inter00f+standard',
    DunkNow2: 'DC-9224723/inter0/inter00p+standard',
    DunkNow3: 'DC-9224723/inter0/inter00t+standard',
    DunkNow4: 'DC-9224723/inter0/inter00x+standard',
  },
  PlayLevel: {
    NewPlayLevel2: 'DC-9224723/inter0/inter00k+standard',
    PlayLevel3: 'DC-9224723/inter0/inter00s+standard',
    PlayLevel4: 'DC-9224723/inter0/inter00w+standard',
    PlayLevelBonus: 'DC-9224723/inter0/inter00_+standard',
  },
  PlayBonus: {
    PlayBonus: 'DC-9224723/inter0/inter00i+standard',
    PlayBonus2: 'DC-9224723/inter0/inter00q+standard',
    PlayBonus3: 'DC-9224723/inter0/inter00u+standard',
    PlayBonus4: 'DC-9224723/inter0/inter00y+standard',
  },
  SubmitPoint: {
    NewSubmitPoint: 'DC-9224723/inter0/inter00j+standard',
    SubmitPoint2: 'DC-9224723/inter0/inter00r+standard',
    SubmitPoint3: 'DC-9224723/inter0/inter00v+standard',
    SubmitPoint4: 'DC-9224723/inter0/inter00z+standard',
    FinalSubmit: 'DC-9223079/oreo/inter01h+standard',
  },
}

const sendConversion = (code: string, params: Record<string, any> = {}) => {
  triggerConversion(code, params)
}

const triggerDynamicConversion = (
  mapKey: 'DunkNow' | 'PlayLevel' | 'PlayBonus' | 'SubmitPoint',
  generateIdFn: (status: string, level: number) => string,
  status: string,
  level: number = 0
) => {
  const id = generateIdFn(status, level)
  const sendTo = eventMap[mapKey][id]
  if (sendTo) {
    triggerConversion(sendTo, {buttonId: id})
    triggerConversionTTQ(id)
    triggerConversionMeta(id)
  } else {
    console.warn(`No matching conversion ID for ${id}`)
  }
}

export const DunkNow = (status: string, level: number = 0) => triggerDynamicConversion(
  'DunkNow', generateDunkNowId, status, level
)

export const PlayLevel = (status: string, level: number = 0) => triggerDynamicConversion(
  'PlayLevel', generatePlayLevelId, status, level
)

export const PlayBonus = (status: string, level: number = 0) => triggerDynamicConversion(
  'PlayBonus', generatePlayBonusId, status, level
)

export const SubmitPoint = (status: string, level: number = 0) => triggerDynamicConversion(
  'SubmitPoint', generateSubmitPointId, status, level
)

export const Pageview = () => sendConversion('DC-9224723/inter0/inter00a+standard')
export const CountrySelection = () => {
  sendConversion('DC-9224723/inter0/inter00b+standard')
}
export const HamburgerPlay = () => {
  sendConversion('DC-9224723/inter0/inter00c+standard')
  triggerConversionTTQ('HamburgerPlay')
  triggerConversionMeta('HamburgerPlay')
}
export const NewPlayer = () => {
  sendConversion('DC-9224723/inter0/inter00e+standard')
  triggerConversionTTQ('NewPlayer')
  triggerConversionMeta('NewPlayer')
}
export const MissedPlayAgain = () => {
  sendConversion('DC-9224723/inter0/inter00g+standard')
  triggerConversionTTQ('MissedPlayAgain')
  triggerConversionMeta('MissedPlayAgain')
}
export const NewPlaySkip = () => {
  sendConversion('DC-9224723/inter0/inter00h+standard')
  triggerConversionTTQ('NewPlaySkip')
  triggerConversionMeta('NewPlaySkip')
}
export const ReturnPlayer = () => {
  sendConversion('DC-9224723/inter0/inter00l+standard')
  triggerConversionTTQ('ReturnPlayer')
  triggerConversionMeta('ReturnPlayer')
}
export const ReturnLogin = () => {
  sendConversion('DC-9224723/inter0/inter00m+standard')
  triggerConversionTTQ('ReturnLogin')
  triggerConversionMeta('ReturnLogin')
}
export const ForgotSubmit = () => {
  sendConversion('DC-9224723/inter0/inter00n+standard')
  triggerConversionTTQ('ForgotSubmit')
  triggerConversionMeta('ForgotSubmit')
}
export const ForgotSubmitOTP = () => {
  sendConversion('DC-9224723/inter0/inter00o+standard')
  triggerConversionTTQ('ForgotSubmitOTP')
  triggerConversionMeta('ForgotSubmitOTP')
}
export const NewRegister = () => {
  sendConversion('')
  triggerConversionTTQ('NewRegister')
  triggerConversionMeta('NewRegister')
}
export const NewRegisSubmit = () => {
  sendConversion('DC-9224723/inter0/inter00y+standard')
  triggerConversionTTQ('NewRegisSubmit')
  triggerConversionMeta('NewRegisSubmit')
}
export const ScanCookie = () => {
  sendConversion('DC-9224723/inter0/inter01+standard')
  triggerConversionTTQ('ScanCookie')
  triggerConversionMeta('ScanCookie')
}
export const ScanPlay = () => {
  sendConversion('DC-9224723/inter0/inter010+standard')
  triggerConversionTTQ('ScanPlay')
  triggerConversionMeta('ScanPlay')
}
export const ScanSkipped = () => {
  sendConversion('DC-9224723/inter0/inter011+standard')
  triggerConversionTTQ('ScanSkipp')
  triggerConversionMeta('ScanSkipp')
}
export const BuyProduct = () => {
  sendConversion('DC-9224723/inter0/inter012+standard')
  triggerConversionTTQ('BuyProduct')
  triggerConversionMeta('BuyProduct')
}
export const Shopee = () => {
  sendConversion('DC-9224723/inter0/inter013+standard')
  triggerConversionTTQ('Shopee')
  triggerConversionMeta('Shopee')
}
export const Lazada = () => {
  sendConversion('DC-9224723/inter0/inter014+standard')
  triggerConversionTTQ('Lazada')
  triggerConversionMeta('Lazada')
}
export const Lotus = () => {
  sendConversion('DC-9224723/inter0/inter015+standard')
  triggerConversionTTQ('Lotus')
  triggerConversionMeta('Lotus')
}
