export const generateDunkNowId = (
  status: string,
  level: number = 0  // 0-3
): string => {
  const id = 'DunkNow'
  if (status === 'firstTimePlaying') {
    return id
  } else if (level === 0) {
    return `${id}`
  } else if (level === 1) {
    return `${id}2`
  } else if (level === 2) {
    return `${id}3`
  } else if (level === 3) {
    return `${id}4`
  }
  console.log(id)
  return id
}

export const generatePlayLevelId = (
  status: string,
  level: number = 0  // 0-3
): string => {
  let id = ''
  if (status === 'firstTimePlaying') {
    id = 'PlayLevel'
  } else if (level === 0) {
    id = 'PlayLevel'
  } else if (level === 1) {
    id = 'NewPlayLevel2'
  } else if (level === 2) {
    id = 'PlayLevel3'
  } else if (level === 3) {
    id = 'PlayLevel4'
  }
  console.log(id)
  return id
}

export const generatePlayBonusId = (
  status: string,
  level: number = 0  // 0-3
): string => {
  let id = ''
  if (status === 'firstTimePlaying') {
    id = 'NewPlayBonus'
  } else if (level === 1) {
    id = 'NewPlayBonus'
  } else if (level === 2) {
    id = 'PlayBonus2'
  } else if (level === 3) {
    id = 'PlayBonus3'
  } else if (level === 4) {
    id = 'PlayBonus4'
  }
  console.log(id)
  return id
}

export const generateSubmitPointId = (
  status: string,
  level: number = 0  // 0-3
): string => {
  let id = ''
  if (status === 'firstTimePlaying') {
    id = 'NewSubmitPoint'
  } else if (status === 'playBonusLevel') {
    id = 'FinalSubmit'
  } else if (level === 1) {
    id = 'NewSubmitPoint'
  } else if (level === 2) {
    id = 'SubmitPoint2'
  } else if (level === 3) {
    id = 'SubmitPoint3'
  } else if (level === 4) {
    id = 'SubmitPoint4'
  }
  console.log({id})
  return id
}
