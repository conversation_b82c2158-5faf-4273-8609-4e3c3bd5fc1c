// Fungsi untuk mengonversi angka ke format singkat
export const formatNumber = (num: number): string => {
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(0)}B`  // Miliar tanpa desimal
  } else if (num >= 1e6) {
    return `${(num / 1e6).toFixed(1)}M`  // Juta dengan satu desimal
  } else if (num >= 1e3) {
    return `${(num / 1e3).toFixed(1)}K`  // Ribu dengan satu desimal
  }
  return num.toString()  // Kembalikan sebagai string jika kurang dari 1000
}

export default formatNumber
