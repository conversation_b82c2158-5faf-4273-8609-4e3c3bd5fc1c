declare let React: any

interface CardProps {
  className?: string;
  content: any;
}

const Card = ({className = '', content}: CardProps) => (
  <div
      style={{
        backdropFilter: 'blur(7.5px)',
        backgroundColor: 'rgba(0, 23, 84, 0.6)',
      }}
      className={`${className} max-w-[400px] w-full p-2 flex flex-col items-center justify-center border border-[#1D46B0] `}
    >
    {content}
  </div>
)
export default Card
