interface ConfettiFunction {
  (options: Record<string, unknown>): void;
}

declare global {
  interface Window {
    confetti: ConfettiFunction;
  }
}

const triggerConfetti = () => {
  const { confetti } = window;
  if (confetti) {
    // Confetti dari tengah
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { x: 0.5, y: 0.6 },
    });

    // Confetti dari kiri dan kanan
    setTimeout(() => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.2, y: 0.6 },
        angle: 45,
      });

      confetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.8, y: 0.6 },
        angle: 135,
      });
    }, 500);
  }
};

export default triggerConfetti;
