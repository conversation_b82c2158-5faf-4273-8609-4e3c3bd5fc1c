declare let React: any
const defaultPrize = require('../../assets/images/prizes/landing-prize-id.png')

// all time
const prizeAllTimeID = require('../../assets/images/prizes/prize-alltime-id.png')
const prizeAllTimeTH = require('../../assets/images/prizes/prize-alltime-vn.png')
const prizeAllTimeVN = require('../../assets/images/prizes/prize-alltime-vn.png')
const prizeAllTimeHK = require('../../assets/images/prizes/prize-alltime-hk.png')
const prizeAllTimeFJ = require('../../assets/images/prizes/prize-alltime-fj.png')
const prizeAllTimeKH = require('../../assets/images/prizes/prize-alltime-kh.png')
const prizeAllTimePH = require('../../assets/images/prizes/prize-alltime-ph.png')
const prizeAllTimeTW = require('../../assets/images/prizes/prize-alltime-tw.png')
const prizeAllTimeMY = require('../../assets/images/prizes/prize-alltime-my.png')
const prizeAllTimeSG = require('../../assets/images/prizes/prize-alltime-sg.png')

// Biweekly
// const prizeBiweeklyID = require('../../assets/images/prizes/prize-biweekly-id.png');
const prizeBiweeklyTH = require('../../assets/images/prizes/prize-biweekly-vn.png')
const prizeBiweeklyVN = require('../../assets/images/prizes/prize-biweekly-vn.png')
const prizeBiweeklyHK = require('../../assets/images/prizes/prize-biweekly-hk.png')
const prizeBiweeklyFJ = require('../../assets/images/prizes/prize-biweekly-fj.png')
const prizeBiweeklyKH = require('../../assets/images/prizes/prize-biweekly-kh.png')
const prizeBiweeklyPH = require('../../assets/images/prizes/prize-biweekly-ph.png')
const prizeBiweeklyTW = require('../../assets/images/prizes/prize-biweekly-tw.png')
const prizeBiweeklyMY = require('../../assets/images/prizes/prize-alltime-my.png')
const prizeBiweeklySG = require('../../assets/images/prizes/prize-alltime-sg.png')

// Landing
const landingPrizeID = require('../../assets/images/prizes/landing-prize-id.png')
const landingPrizeTH = require('../../assets/images/prizes/landing-prize-th.png')
const landingPrizeVN = require('../../assets/images/prizes/landing-prize-vn.png')
const landingPrizeHK = require('../../assets/images/prizes/landing-prize-hk.png')
const landingPrizeFJ = require('../../assets/images/prizes/landing-prize-fj.png')
const landingPrizeKH = require('../../assets/images/prizes/landing-prize-kh.png')
const landingPrizePH = require('../../assets/images/prizes/landing-prize-ph.png')
const landingPrizeTW = require('../../assets/images/prizes/landing-prize-tw.png')
const landingPrizeMY = require('../../assets/images/prizes/landing-prize-my.png')
const landingPrizeSG = require('../../assets/images/prizes/landing-prize-sg.png')

export interface PrizeType {
  prize: string;
  description: string;
}
const getPrizeByRegion = (
  country: string = 'id',
  type: 'alltime' | 'biweekly' | 'landing'
) => {
  const data = [
    {
      country: 'id',
      landing: {
        prize: landingPrizeID,
        description:
          'Perjalanan ke Jepang, Iphone 14, Samsung A55, VR Meta Quest 3',
      },
      alltime: {
        prize: landingPrizeID,
        description:
          'Perjalanan ke Jepang, Iphone 14, Samsung A55, VR Meta Quest 3',
      },
      biweekly: {
        prize: '',
        description: 'Iphone 14, Samsung A55, VR Meta Quest 3',
      },
    },
    {
      country: 'vn',
      landing: {
        prize: landingPrizeVN,
        description:
          'Chuyến đi Nhật Bản, Samsung Galaxy Flip 6, Đèn chiếu ngôi sao, Ba lô',
      },
      alltime: {
        prize: prizeAllTimeVN,
        description: 'Chuyến đi Nhật Bản',
      },
      biweekly: {
        prize: prizeBiweeklyVN,
        description: 'Samsung Galaxy Flip 6, Đèn chiếu ngôi sao, Ba lô',
      },
    },
    {
      country: 'hk',
      landing: {
        prize: landingPrizeHK,
        description: '太空之旅到日本, 数字旅行券 和 投影仪',
      },
      alltime: {
        prize: prizeAllTimeHK,
        description: '太空之旅到日本',
      },
      biweekly: {
        prize: prizeBiweeklyHK,
        description: '投影仪',
      },
    },
    {
      country: 'fj',
      landing: {
        prize: landingPrizeFJ,
        description:
          'Stargazing Trip to New Zealand, Samsung Galaxy Flip 6, Samsung Galaxy Watch 7, Projector, Backpack, Tumbler, Mug',
      },
      alltime: {
        prize: prizeAllTimeFJ,
        description: 'Stargazing Trip To New Zealand',
      },
      biweekly: {
        prize: prizeBiweeklyFJ,
        description:
          'Samsung Galaxy Flip 6, Samsung Galaxy Watch 7, Projector.',
      },
    },
    {
      country: 'kh',
      landing: {
        prize: landingPrizeKH,
        description:
          'Space Trip to Japan, Samsung Galaxy Flip 6, Samsung Galaxy Watch 7, Projector, Backpack, Tumbler, Mug',
      },
      alltime: {
        prize: prizeAllTimeKH,
        description: 'Space Trip To Japan',
      },
      biweekly: {
        prize: prizeBiweeklyKH,
        description:
          'Samsung Galaxy Flip 6, Samsung Galaxy Watch 7, Projector, Backpack.',
      },
    },
    {
      country: 'ph',
      landing: {
        prize: landingPrizePH,
        description:
          'Space Trip to Japan, Samsung Galaxy S24 Ultra, Playstation 5, Backpack, Room Projector, Crossbody bag',
      },
      alltime: {
        prize: prizeAllTimePH,
        description: 'Space Trip to Japan',
      },
      biweekly: {
        prize: prizeBiweeklyPH,
        description:
          'Samsung Galaxy S24 Ultra, Playstation 5, Backpack, Room Projector, Crossbody bag',
      },
    },
    {
      country: 'tw',
      landing: {
        prize: landingPrizeTW,
        description: '太空之旅到日本, 投影仪',
      },
      alltime: {
        prize: prizeAllTimeTW,
        description: '太空之旅到日本',
      },
      biweekly: {
        prize: prizeBiweeklyTW,
        description: '投影仪',
      },
    },
    {
      country: 'th',
      landing: {
        prize: landingPrizeTH,
        description:
          'ทริปท่องอวกาศที่แกรนด์ เซ็นเตอร์ พอยต์ สเปซ พัทยา, โปรเจคเตอร์, กระเป๋าเป้, แก้วน้ำ, แก้วมัค, กระเป๋าสะพายข้าง',
      },
      alltime: {
        prize: prizeAllTimeTH,
        description: 'ทริปท่องอวกาศที่แกรนด์ เซ็นเตอร์ พอยต์ สเปซ พัทยา',
      },
      biweekly: {
        prize: prizeBiweeklyTH,
        description:
          'โปรเจคเตอร์, กระเป๋าเป้, แก้วน้ำ, แก้วมัค, กระเป๋าสะพายข้าง',
      },
    },
    {
      country: 'my',
      landing: {
        prize: landingPrizeMY,
        description: 'Oreo Space Glass Colour Changing Mug',
      },
      alltime: {
        prize: prizeAllTimeMY,
        description: 'Oreo Space Glass Colour Changing Mug',
      },
      biweekly: {
        prize: prizeBiweeklyMY,
        description: 'Oreo Space Glass Colour Changing Mug',
      },
    },
    {
      country: 'sg',
      landing: {
        prize: landingPrizeSG,
        description: 'Oreo Space Glass Colour Changing Mug',
      },
      alltime: {
        prize: prizeAllTimeSG,
        description: 'Oreo Space Glass Colour Changing Mug',
      },
      biweekly: {
        prize: prizeBiweeklySG,
        description: 'Oreo Space Glass Colour Changing Mug',
      },
    },
  ]

  const prize = data.find(prize => prize.country === country)

  if (prize && type) {
    return prize[type]
  }

  return prize
}

export default getPrizeByRegion
