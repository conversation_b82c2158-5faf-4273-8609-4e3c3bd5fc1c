import {appBase} from './lib/routes'
import {Home} from './views/home'

declare let React: any
declare let ReactDOM: any
declare let ReactRouterDOM: any

const {BrowserRouter, Route, Switch, Redirect} = ReactRouterDOM
import ValidatedRoute from './views/ValidateRoute'
import {MusicProvider} from './views/context/MusicContext'

const base = appBase()
console.log({base})

// <Route exact path={`${base}/`} render={() => <Redirect to={`${base}/sg`} replace />} />
class App extends React.Component {
  render() {
    return (
      <BrowserRouter>
        <MusicProvider children={(
          <Switch>
            <Route exact path={`${base}/`} component={Home} />
            <Route exact path={`${base}/:country?/:page?`} component={ValidatedRoute} />
          </Switch>
        )}>
        </MusicProvider>
      </BrowserRouter>
    )
  }
}
// <Route component={NotFound} />

const render = () => {
  document.body.insertAdjacentHTML('beforeend', '<div id="root"></div>')
  ReactDOM.render(

    <App />,
    document.getElementById('root')
  )
}

export {render}
