declare let React: any
import {useMusic} from '../../context/MusicContext'

interface MusicToggleProps {
  className?: string;
}

const MusicToggle = ({className}:MusicToggleProps) => {
  const {isMusicOn, toggleMusic} = useMusic()

  return (
    <button onClick={toggleMusic} className={className}>
      {isMusicOn ? (
        <svg
          width='14'
          height='22'
          viewBox='0 0 14 22'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M14 21.42L4.737 16.21H0V5.21H4.737L14 0V21.42ZM3.999 7.21H2V14.21H3.999V7.21ZM5.999 14.625L12 18V3.42L5.999 6.795V14.625Z'
            fill='white'
          />
        </svg>
      ) : (
        <svg
          width='23'
          height='22'
          viewBox='0 0 23 22'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path
            d='M14 0V21.42L4.737 16.21H0V5.21H4.737L14 0ZM3.999 7.21H2V14.21H3.999V7.21ZM5.999 14.625L12 18V3.42L5.999 6.794V14.625ZM16.879 7.175L19 9.294L21.121 7.174L22.536 8.587L20.414 10.71L22.535 12.831L21.121 14.245L19 12.124L16.879 14.245L15.464 12.831L17.587 10.71L15.466 8.588L16.879 7.175Z'
            fill='white'
          />
        </svg>
      )}
    </button>
  )
}

export default MusicToggle
