declare let React: any
import Button from '../atoms/Button'
// import {GameStatus} from '../../home'
const {useEffect, useState} = React
import Title from '../atoms/Title'
const oreoPackaging = require('../../../assets/images/oreo-packaging.png')
const shopee = require('../../../assets/images/shopee.png')
const lazada = require('../../../assets/images/lazada.png')
const lotus = require('../../../assets/images/lotus.png')
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
import { CountryCode, getCurrentCountry } from '../../config/countryCode';
import {Shopee, Lazada, Lotus} from '../../analytics/events'
import {Language} from '../../config/translation'
import {translate} from '../../config/translation'

interface Props {
  setGameStatus: (status: any) => void;
}

const BuyProduct = ({setGameStatus}: Props) => {
  const {country} = useParams()
  const [countryData, setCountryData] = useState(null)

  useEffect(() => {
    const data = getCurrentCountry(country) as CountryCode;
    setCountryData(data);
  }, [country]);

  return (
    <div className='flex flex-col gap-4 justify-center items-center'>
      <img
        src={oreoPackaging}
        alt='Oreo Space'
        className='w-[230px] h-full object-contain'
      />
      <div className='flex flex-col'>
       <Title text={translate('getYourOreoSpace', country as Language)} />
        <Title text={translate('inStoreOrOnlineHere', country as Language)} />
      </div>

      <div className='flex flex-col gap-4'>
        {countryData?.ecommerce.shopee && (
          <a
            href={countryData?.ecommerce.shopee}
            target='_blank'
            rel='noopener noreferrer'
          >
            <Button
            id='Shopee'
            onClick={Shopee}
              label={
                <img src={shopee} alt='Shopee' className='h-6 object-contain' />
              }
              variant='white'
            />
          </a>
        )}
        {countryData?.ecommerce.lazada && (
          <a
            href={countryData?.ecommerce.lazada}
            target='_blank'
            rel='noopener noreferrer'
          >
            <Button
            id='Lazada'
            onClick={Lazada}
              label={
                <img src={lazada} alt='Lazada' className='h-5 object-contain' />
              }
              variant='white'
            />
          </a>
        )}
        {countryData?.ecommerce.lotus && (
          <a
            href={countryData?.ecommerce.lotus}
            target='_blank'
            rel='noopener noreferrer'
          >
            <Button
            id='Lotus'
            onClick={Lotus}
              label={
                <img src={lotus} alt='Lotus' className='h-6 object-contain' />
              }
              variant='white'
            />
          </a>
        )}
        <Button
          label={translate('backToBonusLevel', country as Language)}
          onClick={() => setGameStatus('bonus-level')}
        />
      </div>
    </div>
  )
}

export default BuyProduct
