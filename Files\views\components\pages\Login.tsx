declare let React: any
const {useState, useRef} = React
import Card from '../atoms/Card'
import Button from '../atoms/Button'
import Title from '../atoms/Title'
import {API_LOGIN} from '../../config/apiConfig'
import Alert from '../atoms/Alert'
import {UserData} from '../../types/UserData'
import {ReturnLogin} from '../../analytics/events'
import {translate} from '../../config/translation'
import {Language} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface LoginProps {
onLogin: (user: UserData) => void;
 onForgotPassword: () => void;
}

const Login = ({onLogin, onForgotPassword}: LoginProps) => {
  const {country} = useParams()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState(['', '', '', ''])
  const [isLoading, setIsLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')
  const [errorMessages, setErrorMessages] = useState({
    email: '',
    pin: '',
  })

  const passwordRefs = useRef([])

  const handleEmailChange = (e) => {
    setEmail(e.target.value)
  }

  const handlePasswordChange = (index: number, value: string) => {
    const newPassword = [...password]
    newPassword[index] = value
    setPassword(newPassword)

    if (value && index < password.length - 1) {
      passwordRefs.current[index + 1]?.focus()
    }

    if (!value && index > 0) {
      passwordRefs.current[index - 1]?.focus()
    }
  }

  const handleKeyDown = (
    e: any,
    index: number
  ) => {
    // Menangani backspace
    if (e.key === 'Backspace' && !password[index] && index > 0) {
      passwordRefs.current[index - 1]?.focus()
    }

    // Menangani tombol panah kanan
    if (e.key === 'ArrowRight' && index < password.length - 1) {
      passwordRefs.current[index + 1]?.focus()
    }

    // Menangani tombol panah kiri
    if (e.key === 'ArrowLeft' && index > 0) {
      passwordRefs.current[index - 1]?.focus()
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setErrorMessages({email: '', pin: ''})  // Reset error messages

    // Validasi Email dan PIN
    let isValid = true

    // Validasi Email
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/  // Pola untuk validasi email
    if (!email) {
      setErrorMessages(prev => ({...prev, email: translate('emailRequired', country as Language)}))
      isValid = false
    } else if (!emailPattern.test(email)) {
      setErrorMessages(prev => ({...prev, email: translate('invalidEmailFormat', country as Language)}))
      isValid = false
    }

    if (password.some(digit => digit === '')) {
      setErrorMessages(prev => ({
        ...prev,
        pin: translate('pinRequired', country as Language),
      }))
      isValid = false
    }

    if (!isValid) {
      setIsLoading(false)
      return
    }

    // Menyiapkan payload
    const payload = {
      email,
      password: password.join(''),
    }

    try {
      const response = await fetch(API_LOGIN, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to login')
      }

      console.log('Login successful:', data)
      localStorage.setItem('token', data.data.token)
      localStorage.setItem('refreshToken', data.data.refreshToken)
      localStorage.setItem('user', JSON.stringify(data.data.user))

      onLogin(data.data.user)
    } catch (error) {
      const errorMessage =
        (error as { response?: { data?: { message?: string } } }).response?.data
          ?.message ||
        error.message ||
        'Internal server error'
      setAlertMessage(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const renderPasswordInputs = () => password.map((digit, index) => (
    <input
        key={index}
        type='password'
        maxLength={1}
        value={digit}
        onChange={e => handlePasswordChange(index, e.target.value)}
        onKeyDown={e => handleKeyDown(e, index)}
        ref={el => (passwordRefs.current[index] = el)}
        className='w-10 h-10 text-center border border-gray-300 rounded text-[28px]'
         inputMode='numeric'
        pattern='[0-9]*'
      />
  ))

  return (
    <div className='flex flex-col gap-4'>
      <div className='flex flex-col items-center gap-2 mb-8'>
        <Title text={translate('login', country as Language)} className='fade-in' />
        <p className='fade-in text-center text-[10px] max-w-[200px] mt-1 ml-1 mb-8'>
          {translate('loginInstructions', country as Language)}
        </p>
      </div>
      <Card
        content={(
          <div className='w-full'>
            <input
              type='text'
              placeholder={translate('enterEmailAddress', country as Language)}
              value={email}
              onChange={handleEmailChange}
              className='w-full p-2 bg-transparent border-b border-white text-white placeholder-white/50 focus:outline-none text-[12px]'
            />
            {errorMessages.email && email !== '' && (
              <p className='text-red-500 text-[10px] mt-1 ml-1'>
                {errorMessages.email}
              </p>
            )}
          </div>
        )}
      />
      <Card
        content={(
          <div className='flex flex-col gap-2 items-center'>
            <p className='text-xs'>{translate('enterLoginPassword', country as Language)}</p>
            <div className='flex gap-2 text-black'>
              {renderPasswordInputs()}
            </div>
            {errorMessages.pin && password.some(digit => digit === '') && (
              <p className='text-red-500 text-[10px] mt-1 ml-1'>
                {errorMessages.pin}
              </p>
            )}
          </div>
        )}
      />
      <div className='mt-8'>
        <p
          onClick={onForgotPassword}
          className='text-sm text-center underline cursor-pointer mb-12'
        >
          {translate('forgotPassword', country as Language)}
        </p>
        <Button
        id='ReturnLogin'
        onClick={() => {
          handleSubmit()
          ReturnLogin()
        }}
        label={translate('login', country as Language)}
        isLoading={isLoading}
        disableHoverEffect={true} />
      </div>

      {alertMessage && <Alert message={alertMessage} />}
    </div>
  )
}

export default Login
