declare let React: any
const {useRef, useState, useEffect} = React
import Button from '../atoms/Button'
import Title from '../atoms/Title'
import Footer from '../organisms/Footer'
import Header from '../organisms/Header'
import {API_CHECK_PHOTO, API_UPDATE_API_COUNT, API_GET_API_COUNT} from '../../config/apiConfig'
import {ScanPlay} from '../../analytics/events'
import {Language} from '../../config/translation'
import {translate} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface ScanCookieProps {
  playBonusLevel: () => void;
  handleBack: () => void;
   userLevel?: number;
   signOut: () => void;
   gotoPage: (page: string) => void;
}

const ScanCookie = ({
  playBonusLevel,
  handleBack,
  userLevel = 0,
  signOut,
  gotoPage,
}:ScanCookieProps) => {
  const {country} = useParams();
  const videoRef = useRef(null)
  const [hasPhoto, setHasPhoto] = useState(false)
  const [photoData, setPhotoData] = useState('')
  const canvasRef = useRef(null)
  const [error, setError] = useState('')
  const [isInitializing, setIsInitializing] = useState(true)
  const [isCheckingPhoto, setIsCheckingPhoto] = useState(false)
  const [isPhotoValid, setIsPhotoValid] = useState(null)
  const [showMessage, setShowMessage] = useState(false)
  const [previewImage, setPreviewImage] = useState(null)
  const [labelState, setLabel] = useState('')

  useEffect(() => {
    startCamera()
    return () => {
      stopCamera()
    }
  }, [])

  const checkPhotoWithAPI = async (photo: Blob) => {
    try {
      setIsCheckingPhoto(true)
      setError('')
      const accessKey = '5rbpkjvx6ze'
      const checkUsageResponse = await fetch(`${API_GET_API_COUNT}?accessKey=${accessKey}`)
      const checkUsageData = await checkUsageResponse.json()

      if (!checkUsageResponse.ok) {
        throw new Error(`Failed to check API count: ${checkUsageData.message}`)
      }

      const {count, limit_count} = checkUsageData.data
      console.log(`Current API Count: ${count}, Limit: ${limit_count}`)

      if (count >= limit_count) {
        setError('API usage limit exceeded')
        setIsCheckingPhoto(false)
        return
      }

      console.log('Updating API count...')

      const updateResponse = await fetch(API_UPDATE_API_COUNT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          secretKey: 'madebyhumansClients',
          accessKey: '5rbpkjvx6ze',
        }),
      })

      console.log('API count update response received')

      const updateData = await updateResponse.json()
      console.log('API count update response:', updateData)

      // 🔹 Konversi Blob ke Base64
      const convertBlobToBase64 = async (blob: Blob): Promise<string> => new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(blob)
        reader.onloadend = () => {
          const base64String = reader.result as string
          resolve(base64String.split(',')[1])
        }
        reader.onerror = err => reject(err)
      })

      const imageBase64 = await convertBlobToBase64(photo)

      // 🔹 Kirim permintaan API dengan format JSON
      const response = await fetch(API_CHECK_PHOTO, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer rpa_KT5M550QMKAXNS4SALE7CX77TL7JERMH5SRBU4LZcftlfo',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: {
            api_key: 'secretapikeyoreospace',
            image: imageBase64,
          },
        }),
      })

      const data = await response.json()
      console.log('Detection:', data)

      const {output} = data
      console.log(output)

      const labelArray = output.labels

      console.log('Labels:', labelArray)

      if (!labelArray || labelArray.length === 0) {
        setIsPhotoValid(false)
        setShowMessage(true)
        return
      }

      if (labelArray.includes('Shooting-Star')) {
        localStorage.setItem('photo', output.imageUrl)
        setLabel(['Shooting-Star'])
        setIsPhotoValid(true)
        setShowMessage(true)
      } else {
        setLabel([labelArray[0]])
        setIsPhotoValid(false)
        setShowMessage(true)
      }
    } catch (err) {
      console.error('Error checking photo:', err)
      setShowMessage(true)
      setIsPhotoValid(false)
    } finally {
      setIsCheckingPhoto(false)
    }
  }

  const startCamera = async () => {
    try {
      setError('')
      setIsInitializing(true)
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: {ideal: 640},
          height: {ideal: 480},
          aspectRatio: 4 / 3,
        },
        audio: false,
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play()
          setIsInitializing(false)
        }
      }
    } catch (err) {
      console.error('Error accessing camera:', err)
      setIsInitializing(false)
      if (err instanceof DOMException) {
        if (err.name === 'NotAllowedError') {
          setError(
            'Camera permission denied. Please allow camera access to continue.'
          )
        } else if (err.name === 'NotFoundError') {
          setError(
            'Camera not found. Make sure your device has a camera.'
          )
        } else {
          setError(
            'An error occurred while accessing the camera. Please try again.'
          )
        }
      }
    }
  }

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      const tracks = stream.getTracks()
      tracks.forEach(track => track.stop())
    }
  }

  const takePhoto = async () => {
    if (!videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current

    const {videoWidth} = video
    const {videoHeight} = video

    canvas.width = videoWidth
    canvas.height = videoHeight

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    ctx.drawImage(video, 0, 0, videoWidth, videoHeight)

    setHasPhoto(true)

    canvas.toBlob((blob) => {
      if (blob) {
        setPhotoData(URL.createObjectURL(blob))
        stopCamera()
        checkPhotoWithAPI(blob)
      }
    }, 'image/jpeg')
  }

  const retakePhoto = () => {
    setHasPhoto(false)
    setPhotoData('')
    setIsPhotoValid(null)
    setShowMessage(false)
    setError('')
    startCamera()
  }

  if (error) {
    return (
      <div className='fixed inset-0 bg-black flex items-center justify-center'>
        <div className='bg-red-500 text-white p-4 rounded-lg text-center mx-4'>
          <p>{error}</p>
          <button
            id='try-again-camera'
            onClick={retakePhoto}
            className='mt-4 bg-white text-red-500 px-4 py-2 rounded-lg font-bold'
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className='h-full w-full bg-black mt-[-45px]'>
      {isInitializing ? (
        <div className='absolute w-full h-full flex items-center justify-center bg-black z-50'>
          <div className='text-white text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4'></div>
            <p>Memulai kamera...</p>
          </div>
        </div>
      ) : (
        <div className='flex flex-col items-center justify-between absolute h-full w-full top-0 left-0 right-0 z-20 px-4'>
          <Header handleBack={handleBack} gameStatus='scan-cookie' signOut={signOut} />
          <div
            className={`flex flex-col items-center justify-center transition-opacity duration-500 ease-in-out h-[90px] ${
              showMessage ? 'opacity-100' : 'opacity-0'
            }`}
          >
            {isPhotoValid === true && (
              <div className='fade-in flex flex-col items-center justify-center'>
                <Title text={translate('youJustGot', country as Language)} />
                <Title text={translate('aShootingStar', country as Language)} />
                <Title text={translate('extraPoint6000', country as Language)} className='!text-[16px] mt-2' />
              </div>
            )}
            {isPhotoValid === false && (
              <div className='fade-in flex flex-col items-center justify-center'>
                <Title text={translate('scanningFailed', country as Language)} />
                <Title text={translate('pleaseTryAgain', country as Language)} className='!text-[16px] mt-2' />
              </div>
            )}
            {isPhotoValid === null && (
              <div className='fade-in flex flex-col items-center justify-center max-w-[200px]'>
                <Title text={translate('scanTheCookies', country as Language)} />
              </div>
            )}
          </div>
          <svg
            width='177'
            height='183'
            viewBox='0 0 177 183'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M0.914795 7.8778C0.914795 5.87679 1.70969 3.95775 3.12461 2.54283C4.53954 1.1279 6.45858 0.333008 8.45958 0.333008L23.5492 0.333008V7.8778H8.45958V22.9674H0.914795V7.8778ZM176.686 174.648C176.686 176.649 175.892 178.568 174.477 179.983C173.062 181.398 171.143 182.193 169.142 182.193H154.052V174.648H169.142V159.558H176.686V174.648ZM8.45958 182.193C6.45858 182.193 4.53954 181.398 3.12461 179.983C1.70969 178.568 0.914795 176.649 0.914795 174.648V159.558H8.45958V174.648H23.5492V182.193H8.45958ZM169.142 0.333008C171.143 0.333008 173.062 1.1279 174.477 2.54283C175.892 3.95775 176.686 5.87679 176.686 7.8778V22.9674H169.142V7.8778H154.052V0.333008H169.142Z'
              fill='#00FF51'
            />
          </svg>

          <div
            className={`flex flex-col items-center justify-center -space-y-2 ${
              isPhotoValid !== null && 'opacity-0'
            }`}
          >
            <Title
              text={translate('extraPointsEarnedByScanningThe', country as Language)}
              className='!text-[14px]'
            />
            <Title
              text={translate('rarestShootingStarCookieEmbossment', country as Language)}
              className='!text-[14px]'
            />
            <Title
              text={translate('youCanOnlyScanOnceEvery48Hours', country as Language)}
              className='!text-[14px]'
            />
          </div>
          {isPhotoValid === true && (
            <Button
              id='ScanPlay'
              onClick={() => {
                setShowMessage(false)
                setIsPhotoValid(null)
                playBonusLevel()
                ScanPlay()
              }}
              disabled={isInitializing}
              label={translate('startPlay', country as Language)}
              className='mb-[100px] max-w-[200px]'
            />
          )}
          {isPhotoValid === false && (
            <Button
              id='ScanRetakePhoto'
              onClick={retakePhoto}
              label={translate('tryAgain', country as Language)}
              className='mb-[100px] max-w-[200px]'
            />
          )}
          {isPhotoValid === null && (
            <Button
              id='ScanTakePhoto'
              onClick={takePhoto}
              disabled={isInitializing}
              isLoading={isCheckingPhoto}
              disableHoverEffect
              label={translate('scanNow', country as Language)}
              className='mb-[100px] max-w-[200px]'
            />
          )}
          <Footer goToPage={gotoPage} />
        </div>
      )}
      {!hasPhoto ? (
        <div className='  w-full h-full'>
          {/* Camera View */}
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className='absolute inset-0 w-full h-full object-cover'
          />

          {/* Loading State */}

          {/* Camera Button */}
          {/* <div className="absolute bottom-8 left-0 right-0 flex justify-center">
            <Button
              onClick={takePhoto}
              disabled={isInitializing}
              label="Take Photo"
            />
          </div> */}
        </div>
      ) : (
        <div className='absolute inset-0 w-full h-full'>
          {/* Captured Photo */}
          <img
            src={photoData}
            alt='Captured'
            className='absolute inset-0 w-full h-full object-cover'
          />
        </div>
      )}

      <canvas ref={canvasRef} className='hidden' />
    </div>
  )
}

export default ScanCookie
