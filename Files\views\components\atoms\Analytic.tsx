declare let React: any
declare let ReactRouterDOM: any

declare let window: any
const {useState, useEffect, useRef} = React
const {withRouter, useLocation, useParams, useHistory} = ReactRouterDOM

function Analytic() {
  const {country} = useParams()

  // Peta region ke ID Pixel
  const pixelIdTiktokMap = {
    th: 'CVEJVB3C77U2FEPHD3O0',
    id: 'CVDR1SRC77U80LAMM0E0',
    vn: 'CVGI00JC77UAB8G2GAH0',
  }

  // Peta region ke ID Pixel
  const pixelIdMetaMap = {
    th: '663462756373757',
    id: '1848241205951322',
    vn: '1163146761697671',
  }

  // Peta region ke ID Pixel
  const pixelIdDV360Map = {
    // th: 'CVEJVB3C77U2FEPHD3O0',
    // id: 'CVDR1SRC77U80LAMM0E0',
    // vn: 'CVGI00JC77UAB8G2GAH0',
  }

  // Fungsi untuk memuat Pixel dengan ID dinamis
  function loadPixel() {
    const pixelIdTiktok = pixelIdTiktokMap[country?.toLocaleLowerCase()]
    console.log('ID Pixel yang digunakan:', pixelIdTiktok)
    if (window.ttq && pixelIdTiktok) {
      console.log('load ulang---', pixelIdTiktok)
      window.ttq.load(pixelIdTiktok)
      window.ttq.page()
      window.ttq.track('CountrySelection')
    }

    const pixelIdMeta = pixelIdMetaMap[country?.toLocaleLowerCase()]
    if (window.fbq && pixelIdMeta) {
      console.log('Meta initial')
      window.fbq('init', '663462756373757')
      window.fbq('track', 'PageView')
      window.fbq('trackCustom', 'CountrySelection')
    }
  }

  useEffect(() => {
    loadPixel()
  }, [country])

  return (
    <div>

    </div>
  )
}

export default Analytic
