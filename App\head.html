<!-- Copyright (c) 2022 8th Wall, Inc. -->
<!-- head.html is optional; elements will be added to your html head before app.js is loaded. -->
<script>
  (function (w, d, s, l, i) {
    w[l] = w[l] || [];
    w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
    var f = d.getElementsByTagName(s)[0],
      j = d.createElement(s),
      dl = l != 'dataLayer' ? '&l=' + l : '';
    j.async = true;
    j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
    f.parentNode.insertBefore(j, f);
  })(window, document, 'script', 'dataLayer', 'GTM-TRLNTBRH');
</script>

<!-- 
Start of global snippet: Please do not remove
Place this snippet between the <head> and </head> tags on every page of your site.
-->
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=**********"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag('js', new Date());

  gtag('config', '**********');
</script>
<!-- End of global snippet: Please do not remove -->

<!-- Use "8thwall:" meta tags to hook into 8th Wall's build process and developer tools. -->
<meta name="8thwall:renderer" content="aframe:1.5.0" />
<meta name="8thwall:package" content="@8thwall.xrextras" />
<meta name="8thwall:package" content="@8thwall.landing-page" />
<meta name="8thwall:package" content="@react.react:17.0.0" />
<meta name="8thwall:package" content="@react.react-dom:17.0.0" />
<meta name="8thwall:package" content="@react.react-router-dom" />

<!-- Other external scripts and meta tags can also be added. -->
<!-- <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" /> -->
<meta
  name="viewport"
  content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, minimum-scale=1.0" />
<!-- Mencegah double tap zoom di iOS -->
<script>
  // Mencegah double-tap zoom pada iOS Safari
  document.addEventListener(
    'touchstart',
    function (event) {
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    },
    { passive: false }
  );

  // Mencegah double-tap zoom dengan mendeteksi double tap
  let lastTouchEnd = 0;
  document.addEventListener(
    'touchend',
    function (event) {
      const now = new Date().getTime();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    },
    { passive: false }
  );
</script>

<link
  rel="stylesheet"
  href="//fonts.googleapis.com/css?family=Nunito:300,400,500,700&amp;display=swap" />
<link
  rel="stylesheet"
  href="//fonts.googleapis.com/css?family=Roboto:300,400,500,700&amp;display=swap" />
<link rel="stylesheet" href="//fonts.googleapis.com/icon?family=Material+Icons" />
<!-- <script src="//unpkg.com/@material-ui/core@latest/umd/material-ui.production.min.js"></script> -->

<!-- NEW -->
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://unpkg.com/three@0.132.2/examples/js/loaders/GLTFLoader.js"></script>
<script>
  window._XR8Chunks = [];
</script>

<script>
  !(function (w, d, t) {
    w.TiktokAnalyticsObject = t;
    var ttq = (w[t] = w[t] || []);
    (ttq.methods = [
      'page',
      'track',
      'identify',
      'instances',
      'debug',
      'on',
      'off',
      'once',
      'ready',
      'alias',
      'group',
      'enableCookie',
      'disableCookie',
      'holdConsent',
      'revokeConsent',
      'grantConsent',
    ]),
      (ttq.setAndDefer = function (t, e) {
        t[e] = function () {
          t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
        };
      });
    for (var i = 0; i < ttq.methods.length; i++) ttq.setAndDefer(ttq, ttq.methods[i]);
    (ttq.instance = function (t) {
      for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++)
        ttq.setAndDefer(e, ttq.methods[n]);
      return e;
    }),
      (ttq.load = function (e, n) {
        var r = 'https://analytics.tiktok.com/i18n/pixel/events.js',
          o = n && n.partner;
        (ttq._i = ttq._i || {}),
          (ttq._i[e] = []),
          (ttq._i[e]._u = r),
          (ttq._t = ttq._t || {}),
          (ttq._t[e] = +new Date()),
          (ttq._o = ttq._o || {});
        n = document.createElement('script');
        (n.type = 'text/javascript'), (n.async = !0), (n.src = r + '?sdkid=' + e + '&lib=' + t);
        e = document.getElementsByTagName('script')[0];
        e.parentNode.insertBefore(n, e);
      });
  })(window, document, 'ttq');
</script>

<script>
  !(function (f, b, e, v, n, t, s) {
    if (f.fbq) return;
    n = f.fbq = function () {
      n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
    };
    if (!f._fbq) f._fbq = n;
    n.push = n;
    n.loaded = !0;
    n.version = '2.0';
    n.queue = [];
    t = b.createElement(e);
    t.async = !0;
    t.src = v;
    s = b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t, s);
  })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
</script>
