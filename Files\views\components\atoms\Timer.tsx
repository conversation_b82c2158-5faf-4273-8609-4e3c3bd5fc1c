declare let React: any
const {useRef, useEffect, useState} = React
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
import { translateAutoplay } from '../../config/translation';
import { Language } from '../../config/translation';

const Timer = ({
  duration,
  className = '',
  onTimeUp,
  variant = 'clock',
}: {
duration: number;
className?: string;
onTimeUp?: () => void;  // Fungsi yang dijalankan ketika waktu habis
variant?: 'clock' | 'autoplay';
}) => {
  const { country } = useParams();
  const [timeLeft, setTimeLeft] = useState(duration)

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prevTimeLeft => prevTimeLeft - 1)
      }, 1000)

      return () => clearInterval(timer)
    } else if (timeLeft === 0 && onTimeUp) {
      onTimeUp()  // Memanggil fungsi ketika waktu habis
    }
  }, [timeLeft, onTimeUp])

  const radius = 30
  const circumference = 2 * Math.PI * radius

  return (
    <>
      {variant === 'clock' && (
        <div
          className={`${className} flex justify-center items-center relative `}
        >
          <svg className='w-[70px] h-[70px]'>
            {/* Lingkaran dasar berwarna putih */}
            <circle
              className='stroke-white fill-transparent'
              strokeWidth='8'
              cx='35'
              cy='35'
              r={radius}
            />

            {/* Lingkaran indikator biru dengan stroke dash */}
            <circle
              className='stroke-blue-500 fill-transparent'
              strokeWidth='5'
              strokeDasharray={circumference}
              strokeDashoffset={(1 - timeLeft / duration) * circumference}
              cx='35'
              cy='35'
              r={radius}
              transform='rotate(-90 35 35)'  // Mengubah rotasi ke arah berlawanan jam
            />
          </svg>
          <div className='absolute text-center'>
            <span className='text-2xl font-bold text-white'>{timeLeft}</span>
          </div>
        </div>
      )}
      {variant === 'autoplay' && (
        <div className={`${className} flex justify-center items-center`}>
          <div className='text-center'>
            <span className='text-[10px] text-white font-medium'>
              {translateAutoplay(country as Language, timeLeft)}
            </span>
          </div>
        </div>
      )}
    </>
  )
}

export default Timer
