declare let React: any
import { Language, translateCookieNotice } from '../../config/translation';
import { translate } from '../../config/translation';
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM
import Title from '../atoms/Title';
import Card from '../atoms/Card';
import Button from '../atoms/Button';
interface Props {
  gotoHome: () => void;
}

const CookiePolicy = ({ gotoHome }: Props) => {
  const { country } = useParams();
  return (
    <div className="fade-in  flex flex-col items-center px-4 mt-[58px] absolute w-full">
      <div className="flex flex-col gap-4 overflow-y-auto h-[calc(100svh-58px-76px)] py-4 px-4 ">
        <Title
          className="uppercase"
          text={translate('cookieNotice', country as Language)}
        />
        <Card
          className=" overflow-auto !justify-start"
          content={translateCookieNotice(country as Language)}
        />
        <Button
          className="!w-[300px]"
          label={translate(
            'howToParticipate_GotoHomePage',
            country as Language
          )}
          onClick={() => gotoHome()}
        />
      </div>
    </div>
  );
};

export default CookiePolicy;
