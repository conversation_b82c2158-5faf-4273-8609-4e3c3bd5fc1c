declare let React: any
const {useRef, useState} = React
import Button from '../atoms/Button'
import Title from '../atoms/Title'
import {HamburgerPlay} from '../../analytics/events'

// Import gambar oreo
const shootingStarOreo = require('../../../assets/images/oreo-variant/shootingStar.png')
const rocketOreo = require('../../../assets/images/oreo-variant/rocket.png')
const astronautOreo = require('../../../assets/images/oreo-variant/astronaut.png')
const starOreo = require('../../../assets/images/oreo-variant/star.png')
const telescopeOreo = require('../../../assets/images/oreo-variant/telescope.png')
import {translate, Language} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface OreoVariant {
  id: number;
  name: string;
  description: string ;
  image: string;
}

interface OreoSpaceDunkProps {
  gotoHome: () => void;
}

const OreoSpaceDunk = ({gotoHome}: OreoSpaceDunkProps) => {
  const {country} = useParams()
  const ref = useRef(null)

  const oreoVariants: OreoVariant[] = [
    {
      id: 0,
      name: '',
      description: '',
      image: shootingStarOreo,
    },
    {
      id: 1,
      name: translate(
        'oreoSpaceDunk_OreoShootingStarTitle',
        country as Language
      ),
      description: (
        <>
          {translate('oreoSpaceDunk_OreoShootingStarDesc', country as Language)}
          <br />
          <br />
          {translate('oreoSpaceDunk_AvailabelInYour', country as Language)}
        </>
      ),
      image: shootingStarOreo,
    },
    {
      id: 2,
      name: translate('oreoSpaceDunk_OreoRocketTitle', country as Language),
      description: (
        <>
          {translate('oreoSpaceDunk_OreoRocketDesc', country as Language)}
          <br />
          <br />
          {translate('oreoSpaceDunk_AvailabelInYour', country as Language)}
        </>
      ),
      image: rocketOreo,
    },
    {
      id: 3,
      name: translate('oreoSpaceDunk_OreoAstronautTitle', country as Language),
      description: (
        <>
          {translate('oreoSpaceDunk_OreoAstronautDesc', country as Language)}
          <br />
          <br />
          {translate('oreoSpaceDunk_AvailabelInYour', country as Language)}
        </>
      ),
      image: astronautOreo,
    },
    {
      id: 4,
      name: translate('oreoSpaceDunk_OreoStarTitle', country as Language),
      description: (
        <>
          {translate('oreoSpaceDunk_OreoStarDesc', country as Language)}
          <br />
          <br />
          {translate('oreoSpaceDunk_AvailabelInYour', country as Language)}
        </>
      ),
      image: starOreo,
    },
    {
      id: 5,
      name: translate('oreoSpaceDunk_OreoTelescopeTitle', country as Language),
      description: (
        <>
          {translate('oreoSpaceDunk_OreoTelescopeDesc', country as Language)}
          <br />
          <br />
          {translate('oreoSpaceDunk_AvailabelInYour', country as Language)}
        </>
      ),
      image: telescopeOreo,
    },
    {
      id: 6,
      name: '',
      description: '',
      image: telescopeOreo,
    },
  ]

  const [activeOreo, setActiveOreo] = useState(oreoVariants[1])

  const handleClick = (direction: string) => {
    if (ref.current) {
      const index = oreoVariants.findIndex(
        variant => variant.id === activeOreo.id
      )

      if (direction === 'right' && index !== oreoVariants.length - 2) {
        setActiveOreo(oreoVariants[index + 1])
      } else if (direction === 'left' && index !== 1) {
        setActiveOreo(oreoVariants[index - 1])
      }

      const {scrollLeft, offsetWidth} = ref.current
      const scrollAmount =
        direction === 'right'
          ? scrollLeft + offsetWidth / 3
          : scrollLeft - offsetWidth / 3

      ref.current.scrollTo({left: scrollAmount, behavior: 'smooth'})
    }
  }

  return (
    <div className='w-full h-full flex flex-col items-center pt-4 gap-4 fade-in'>
      <Title
        text={translate('oreoSpaceDunk_Title', country as Language)}
      />

      <div className='relative w-full max-w-[800px]'>
        <div className='overflow-hidden'>
          <div
            ref={ref}
            className='flex py-2 scrollbar-hide overflow-scroll'
            style={{backfaceVisibility: 'hidden'}}
          >
            {oreoVariants.map(variant => (
              <div
                key={variant.id}
                className={`${
                  [0, 6].includes(variant.id) ? 'opacity-0' : 'opacity-100'
                } relative w-1/3 shrink-0 p-3 transition-all duration-700 ${
                  variant.id === activeOreo.id ? 'scale-125' : 'scale-75'
                }`}
              >
                <img
                  src={variant.image}
                  alt={variant.name}
                  className='object-contain'
                />
              </div>
            ))}
          </div>
        </div>
        <div className='flex flex-col gap-4 px-4'>
          <div className='relative'>
            <div className='absolute z-10 top-1 h-full w-full px-4 left-0 flex flex-col items-center justify-center text-center gap-2'>
              <h3 className='text-white text-base font-bold'>
                {activeOreo.name}
              </h3>
              <p className='text-white text-[12px]'>{activeOreo.description}</p>
            </div>
            <svg
              width='100%'
              height='180'
              viewBox='0 0 310 180'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
              preserveAspectRatio='none'
            >

              <path
                data-figma-bg-blur-radius='15'
                d='M232.5 10.6754H309.5V214.248H0.5V10.6754H77.5H116.25H140.625H140.784L140.914 10.5835L155 0.612589L169.086 10.5835L169.216 10.6754H169.375H193.75H232.5Z'
                fill='#001754'
                fill-opacity='0.6'
                stroke='#1D46B0'
              />
              <defs>
                <clipPath id='bgblur_0_3214_2295_clip_path'>
                  <path
                    transform='translate(15 15)'
                    d='M232.5 10.6754H309.5V214.248H0.5V10.6754H77.5H116.25H140.625H140.784L140.914 10.5835L155 0.612589L169.086 10.5835L169.216 10.6754H169.375H193.75H232.5Z'
                  />
                </clipPath>
              </defs>
            </svg>
          </div>

          {/* <Card
            content={
              <div className="flex flex-col items-center text-center gap-4">
                <h3 className="text-white text-xl font-bold">
                  {activeOreo.name}
                </h3>
                <p className="text-white text-sm">{activeOreo.description}</p>
              </div>
            }
            className="mx-auto"
          /> */}

          <div className='grid grid-cols-2 gap-4'>
            <Button
              variant='transparent'
              disableHoverEffect
              label={(
                <svg
                  width='30'
                  height='18'
                  viewBox='0 0 30 18'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M8.55135 16.1414L1.41003 9.00004L8.55135 1.85873M2.40188 9.00004L28.5899 9.00004'
                    stroke='white'
                    stroke-width='1.5'
                    stroke-miterlimit='10'
                    stroke-linecap='square'
                  />
                </svg>
              )}
              onClick={() => handleClick('left')}
              className=''
              disabled={activeOreo.id === 1}
            />
            <Button
              variant='transparent'
              disableHoverEffect
              label={(
                <svg
                  width='30'
                  height='18'
                  viewBox='0 0 30 18'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M21.4487 1.85876L28.59 9.00008L21.4487 16.1414M27.5981 9.00008L1.41008 9.00007'
                    stroke='white'
                    stroke-width='1.5'
                    stroke-miterlimit='10'
                    stroke-linecap='square'
                  />
                </svg>
              )}
              onClick={() => handleClick('right')}
              className=''
              disabled={activeOreo.id === 5}
            />
          </div>
          <Button
            id='HamburgerPlay'
            variant='transparent'
            label={translate('playNow', country as Language)}
            onClick={() => {
              gotoHome()
              HamburgerPlay()
            }}
            className='w-full'
          />
        </div>
      </div>
    </div>
  )
}

export default OreoSpaceDunk
