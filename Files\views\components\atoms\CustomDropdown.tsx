declare let React: any
const {useState, useRef, useEffect} = React

interface Option {
  code: string;
  country: string;
}

interface CustomDropdownProps {
  options: Option[];
  selectedOption: string;
  onSelect: (code: string) => void;
   disabled?: boolean;
}

const CustomDropdown = ({
  options,
  selectedOption,
  onSelect,
  disabled = false,
}: CustomDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)

  const toggleDropdown = () => {
    if (disabled) return
    setIsOpen(prev => !prev)
  }

  const handleOptionClick = (code: string) => {
    if (disabled) return
    onSelect(code)
    setIsOpen(false)
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className='relative' ref={dropdownRef}>
      <div
        onClick={toggleDropdown}
        className={`p-2 bg-transparent border-b border-white text-white ${
          disabled ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'
        } text-[12px] flex justify-between items-center`}
      >
        <span>{selectedOption}</span>
        {!disabled && <span className='ml-2'>&#9662;</span>}
        <span className='ml-2'>&#9662;</span> {/* Ikon segitiga ke bawah */}
      </div>
      {isOpen && !disabled && (
        <div className='absolute z-10 bg-gray-800 border border-white rounded mt-1 min-w-max max-h-[200px] overflow-auto'>
          {options.map(option => (
            <div
              key={option.code}
              onClick={() => handleOptionClick(option.code)}
              className='p-2 text-white hover:bg-blue-500 cursor-pointer border-b border-white last:border-b-0'
            >
              {option.code} {option.country}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default CustomDropdown
