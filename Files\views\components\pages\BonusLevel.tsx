declare let React: any
import Button from '../atoms/Button'
const scanOreoStar = require('../../../assets/images/scan-oreo-star.png')
import Card from '../atoms/Card'
// import {GameStatus} from '../../home'
import Title from '../atoms/Title'
import {Scan<PERSON><PERSON><PERSON>, ScanSkipped, BuyProduct} from '../../analytics/events'
import {Language} from '../../config/translation'
import {translate} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface Props {
  setGameStatus: (status: any) => void;
  playAgain: () => void;
}

const BonusLevel = ({setGameStatus, playAgain}: Props) => {
  const {country} = useParams()
  return (
    <div className='flex flex-col gap-4'>
      <Title text={translate('bonusLevel', country as Language)} className='mt-4 fade-in' />
      <Card
          className='relative w-full max-w-md pointer-events-auto'
          content={(
            <div className='flex flex-col items-center font-medium'>
              <div className='relative w-[140px] h-[140px] mb-6'>
                <img
                  src={scanOreoStar}
                  alt='Scan Oreo Star'
                  className='w-full h-full object-contain'
                />
              </div>

              <p className='text-center text-xs'>
                {translate(
                  'get6000PointsByScanningTheActualOreoSpaceShootingStarCookie',
                  country as Language
                )}
              </p>
            </div>
          )}
        />
      <div className='flex flex-col gap-4'>
        <Button
            id='ScanCookie'
            label={translate('scanTheCookie', country as Language)}
            onClick={() => {
              setGameStatus('scan-cookie')
              ScanCookie()
            }}
          />
        <Button
            id='ScanSkipped'
            label={translate('skipToPlayWithoutScan', country as Language)}
            onClick={() => {
              playAgain()
              ScanSkipped()
            }}
          />
        <Button
            id='BuyProduct'
            label={translate('buyProduct', country as Language)}
            onClick={() => {
              setGameStatus('buy-product')
              BuyProduct()
            }}
          />
      </div>
    </div>
  )
}
export default BonusLevel
