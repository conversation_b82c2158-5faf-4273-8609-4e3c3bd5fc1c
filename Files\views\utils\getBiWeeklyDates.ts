export const getBiWeeklyDates = () => {
  const now = new Date()
  const startOfYear = new Date(now.getFullYear(), 0, 1)  // 1 Januari tahun ini

  // Hitung selisih hari dari 1 Januari sampai hari ini
  const diffTime = now.getTime() - startOfYear.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  // Hitung periode ke berapa (1 periode = 14 hari)
  const currentPeriod = Math.floor(diffDays / 14)

  // Hitung tanggal awal periode
  const startDate = new Date(startOfYear)
  startDate.setDate(startOfYear.getDate() + currentPeriod * 14)

  // Hitung tanggal akhir periode
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + 13)  // 14 hari - 1 (karena startDate sudah dihitung sebagai hari pertama)

  return {
    weekFrom: startDate.toISOString().split('T')[0],
    weekTo: endDate.toISOString().split('T')[0],
  }
}
