declare let ReactRouterDOM: any
declare let THREE: any
declare let React: any
const {useRef, useEffect} = React

interface JoystickPosition {
  x: number;
  y: number;
}

interface JoystickProps {
  onMove: (
    targetVelocity: JoystickPosition,
    oreoVelocity: JoystickPosition
  ) => void;

  onEnd: () => void;
  // setOreoVelocity: (position: JoystickPosition) => void;
}

const Joystick = ({onMove, onEnd}: JoystickProps) => {
  const joystickRef = useRef(null)
  const knobRef = useRef(null)

  const joystickActive = useRef(false)
  const joystickOrigin = useRef({x: 0, y: 0})
  const currentJoystickPos = useRef({x: 0, y: 0})

  const MAX_DISTANCE = 25
  const MAX_VELOCITY = 0.0001
  const OREO_MAX_VELOCITY = 0.02

  let animationFrameId: number | null = null

  const handleStart = (e: MouseEvent | TouchEvent) => {
    joystickActive.current = true
    const rect = joystickRef.current!.getBoundingClientRect()

    const clientX =
      e.type === 'mousedown'
        ? (e as MouseEvent).clientX
        : (e as TouchEvent).touches[0].clientX
    const clientY =
      e.type === 'mousedown'
        ? (e as MouseEvent).clientY
        : (e as TouchEvent).touches[0].clientY

    joystickOrigin.current = {
      x: clientX - rect.left,
      y: clientY - rect.top,
    }

    if (knobRef.current) {
      knobRef.current.style.transition = 'none'
    }

    if (e.type == 'touchstart') {
      e.preventDefault()
    }
  }

  const handleMove = (e: MouseEvent | TouchEvent) => {
    if (!joystickActive.current) return

    const rect = joystickRef.current!.getBoundingClientRect()
    const clientX =
      'clientX' in e ? e.clientX : (e as TouchEvent).touches[0].clientX
    const clientY =
      'clientY' in e ? e.clientY : (e as TouchEvent).touches[0].clientY

    const newPos = {
      x: clientX - rect.left,
      y: clientY - rect.top,
    }
    currentJoystickPos.current = newPos

    const deltaX = newPos.x - joystickOrigin.current.x
    const deltaY = newPos.y - joystickOrigin.current.y

    const distance = Math.sqrt(deltaX ** 2 + deltaY ** 2)
    const angle = Math.atan2(deltaY, deltaX)

    const limitedDistance = Math.min(distance, MAX_DISTANCE)
    const knobX = limitedDistance * Math.cos(angle)
    const knobY = limitedDistance * Math.sin(angle)

    if (knobRef.current) {
      knobRef.current.style.transform = `translate(calc(-50% + ${knobX}px), calc(-50% + ${knobY}px))`
    }

    const normalizedDeltaX = deltaX / 50
    const normalizedDeltaY = deltaY / 50

    const targetVelocity = {
      x: -normalizedDeltaY * MAX_VELOCITY,
      y: -normalizedDeltaX * MAX_VELOCITY,
    }
    const oreoVelocity = {
      x: normalizedDeltaX * OREO_MAX_VELOCITY,
      y: -normalizedDeltaY * OREO_MAX_VELOCITY,
    }

    // Batasi pemanggilan onMove hanya pada frame tertentu
    if (!animationFrameId) {
      onMove(targetVelocity, oreoVelocity)
      animationFrameId = requestAnimationFrame(() => {
        animationFrameId = null
      })
    }

    if (e.type === 'touchmove') {
      e.preventDefault()
    }
  }

  const handleEnd = () => {
    joystickActive.current = false

    if (knobRef.current) {
      knobRef.current.style.transition = 'transform 0.2s ease'
      knobRef.current.style.transform = 'translate(-50%, -50%)'
    }

    onEnd()
  }

  useEffect(() => {
    const joystickElement = joystickRef.current

    joystickElement?.addEventListener('mousedown', handleStart)
    document.addEventListener('mousemove', handleMove, {passive: false})
    document.addEventListener('mouseup', handleEnd)

    joystickElement?.addEventListener('touchstart', handleStart)
    document.addEventListener('touchmove', handleMove, {passive: false})
    document.addEventListener('touchend', handleEnd)

    return () => {
      joystickElement?.removeEventListener('mousedown', handleStart)
      document.removeEventListener('mousemove', handleMove)
      document.removeEventListener('mouseup', handleEnd)

      joystickElement?.removeEventListener('touchstart', handleStart)
      document.removeEventListener('touchmove', handleMove)
      document.removeEventListener('touchend', handleEnd)
    }
  }, [])

  return (
    <div
      ref={joystickRef}
      id='joystick'
      className='flex justify-center items-center'
    >
      <svg
        width='82'
        height='82'
        viewBox='0 0 82 82'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M48.6544 6.83756C49.3238 7.45483 48.8871 8.57271 47.9765 8.57271L34.0264 8.57272C33.1154 8.57271 32.6789 7.45402 33.3491 6.83702L40.3297 0.410414C40.7127 0.0578047 41.3022 0.0580394 41.6849 0.410954L48.6544 6.83756Z'
          fill='white'
          fill-opacity='0.2'
        />
        <path
          d='M75.1624 48.6545C74.5452 49.3239 73.4273 48.8871 73.4273 47.9766L73.4273 34.0265C73.4273 33.1155 74.546 32.679 75.163 33.3492L81.5896 40.3298C81.9422 40.7128 81.9419 41.3023 81.589 41.685L75.1624 48.6545Z'
          fill='white'
          fill-opacity='0.2'
        />
        <path
          d='M33.3456 75.1624C32.6762 74.5452 33.1129 73.4273 34.0235 73.4273L47.9736 73.4273C48.8846 73.4273 49.3211 74.546 48.6509 75.163L41.6703 81.5896C41.2873 81.9422 40.6978 81.942 40.3151 81.589L33.3456 75.1624Z'
          fill='white'
          fill-opacity='0.2'
        />
        <path
          d='M6.83757 33.3456C7.45484 32.6761 8.57273 33.1129 8.57273 34.0234L8.57273 47.9736C8.57273 48.8845 7.45403 49.3211 6.83703 48.6509L0.410429 41.6702C0.0578198 41.2872 0.0580546 40.6978 0.410969 40.315L6.83757 33.3456Z'
          fill='white'
          fill-opacity='0.2'
        />
      </svg>

      <div ref={knobRef} id='joystick-knob' />
    </div>
  )
}

export default Joystick
