declare let React: any
import Title from '../atoms/Title'
import Button from '../atoms/Button'
import Card from '../atoms/Card'
import {translate} from '../../config/translation'
import {Language} from '../../config/translation'
declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface Props {
  gotoHome: () => void;
}

const arrowBottonLeft = (
  <svg
    width="96"
    height="25"
    viewBox="0 0 96 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_1367_2063)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M91.5 2.97778C91.5 2.14935 90.8284 1.47778 90 1.47778H11.3723C10.5438 1.47778 9.87226 2.14935 9.87226 2.97778V12.0598L7.52418 9.71176C6.93839 9.12598 5.98866 9.12598 5.40287 9.71176C4.81707 10.2975 4.81707 11.2473 5.40287 11.8331L10.3116 16.7418C10.5929 17.0231 10.9744 17.1812 11.3723 17.1812C11.7701 17.1812 12.1516 17.0231 12.4329 16.7418L17.3416 11.8331C17.9274 11.2473 17.9274 10.2975 17.3416 9.71176C16.7559 9.12598 15.8061 9.12598 15.2203 9.71176L12.8723 12.0598V4.47778H90C90.8284 4.47778 91.5 3.80621 91.5 2.97778Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_1367_2063"
        x="0.963531"
        y="0.477783"
        width="94.5365"
        height="23.7034"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="3" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.114278 0 0 0 0 0.274479 0 0 0 0 0.689115 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1367_2063"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_1367_2063"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

const arrowBottonRight = (
  <svg
    width="96"
    height="25"
    viewBox="0 0 96 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_1367_2065)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4.5 3.18115C4.5 2.35272 5.17157 1.68115 6 1.68115H84.6277C85.4562 1.68115 86.1277 2.35272 86.1277 3.18115V12.2632L88.4758 9.91513C89.0616 9.32935 90.0113 9.32935 90.5971 9.91513C91.1829 10.5009 91.1829 11.4507 90.5971 12.0365L85.6884 16.9452C85.4071 17.2265 85.0256 17.3845 84.6277 17.3845C84.2299 17.3845 83.8484 17.2265 83.5671 16.9452L78.6584 12.0365C78.0726 11.4507 78.0726 10.5009 78.6584 9.91513C79.2441 9.32935 80.1939 9.32935 80.7797 9.91513L83.1277 12.2632V4.68115H6C5.17157 4.68115 4.5 4.00958 4.5 3.18115Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_1367_2065"
        x="0.5"
        y="0.681152"
        width="94.5365"
        height="23.7034"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="3" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.114278 0 0 0 0 0.274479 0 0 0 0 0.689115 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1367_2065"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_1367_2065"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

const PlayIcon = (
  <svg
    width="17"
    height="13"
    viewBox="0 0 17 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.783 4.76981L16.7766 4.69941C16.7057 3.69076 16.2556 2.74634 15.5168 2.05595C14.778 1.36557 13.8053 0.980385 12.7942 0.977824H4.79423C3.7689 0.97319 2.78142 1.36484 2.038 2.07099C1.29458 2.77715 0.852702 3.7432 0.804643 4.76741C0.797176 4.82981 0.793709 4.89728 0.794243 4.96981V10.1778C0.794243 10.9178 1.09264 11.6402 1.61184 12.1586C2.13869 12.6825 2.85124 12.9769 3.59423 12.9778C5.03423 12.9778 5.59743 12.1778 6.39422 10.5778C6.51102 10.3442 7.18782 8.9778 8.79422 8.9778C10.391 8.9778 11.0766 10.3434 11.1942 10.5778C11.9974 12.1778 12.5542 12.9778 13.9942 12.9778C14.7342 12.9778 15.4566 12.6794 15.975 12.1602C16.4989 11.6333 16.7933 10.9208 16.7942 10.1778V4.97781C16.7942 4.90261 16.7905 4.83328 16.783 4.76981ZM4.79423 6.60261C4.58116 6.60742 4.36927 6.56963 4.17101 6.49143C3.97275 6.41323 3.79211 6.29621 3.63969 6.14724C3.48728 5.99827 3.36616 5.82036 3.28346 5.62393C3.20075 5.42751 3.15812 5.21654 3.15806 5.00342C3.15801 4.79029 3.20054 4.57931 3.28315 4.38284C3.36575 4.18638 3.48678 4.0084 3.63912 3.85935C3.79146 3.71031 3.97205 3.5932 4.17027 3.5149C4.36849 3.43661 4.58036 3.3987 4.79343 3.40342C5.21148 3.41266 5.60929 3.58519 5.90172 3.88409C6.19415 4.18298 6.35795 4.58447 6.35805 5.00262C6.35816 5.42077 6.19456 5.82234 5.90228 6.12138C5.61 6.42042 5.21227 6.59315 4.79423 6.60261ZM12.7942 2.60262C13.0064 2.60262 13.2099 2.6869 13.3599 2.83693C13.5099 2.98696 13.5942 3.19044 13.5942 3.40262C13.5942 3.61479 13.5099 3.81827 13.3599 3.9683C13.2099 4.11833 13.0064 4.20261 12.7942 4.20261C12.582 4.20261 12.3785 4.11833 12.2285 3.9683C12.0785 3.81827 11.9942 3.61479 11.9942 3.40262C11.9942 3.19044 12.0785 2.98696 12.2285 2.83693C12.3785 2.6869 12.582 2.60262 12.7942 2.60262ZM11.1942 5.80261C10.982 5.80261 10.7786 5.71832 10.6285 5.56829C10.4785 5.41827 10.3942 5.21478 10.3942 5.00261C10.3942 4.79044 10.4785 4.58696 10.6285 4.43693C10.7786 4.2869 10.982 4.20261 11.1942 4.20261C11.4064 4.20261 11.6099 4.2869 11.7599 4.43693C11.9099 4.58696 11.9942 4.79044 11.9942 5.00261C11.9942 5.21478 11.9099 5.41827 11.7599 5.56829C11.6099 5.71832 11.4064 5.80261 11.1942 5.80261ZM12.7942 7.4026C12.582 7.4026 12.3785 7.31832 12.2285 7.16829C12.0785 7.01826 11.9942 6.81478 11.9942 6.60261C11.9942 6.39043 12.0785 6.18695 12.2285 6.03692C12.3785 5.88689 12.582 5.80261 12.7942 5.80261C13.0064 5.80261 13.2099 5.88689 13.3599 6.03692C13.5099 6.18695 13.5942 6.39043 13.5942 6.60261C13.5942 6.81478 13.5099 7.01826 13.3599 7.16829C13.2099 7.31832 13.0064 7.4026 12.7942 7.4026ZM14.3942 5.80261C14.182 5.80261 13.9785 5.71832 13.8285 5.56829C13.6785 5.41827 13.5942 5.21478 13.5942 5.00261C13.5942 4.79044 13.6785 4.58696 13.8285 4.43693C13.9785 4.2869 14.182 4.20261 14.3942 4.20261C14.6064 4.20261 14.8099 4.2869 14.9599 4.43693C15.1099 4.58696 15.1942 4.79044 15.1942 5.00261C15.1942 5.21478 15.1099 5.41827 14.9599 5.56829C14.8099 5.71832 14.6064 5.80261 14.3942 5.80261Z"
      fill="white"
    />
  </svg>
);
const SubmitIcon = (
  <svg
    width="15"
    height="16"
    viewBox="0 0 15 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.3333 0.477783H1.66667C0.75 0.477783 0 1.22778 0 2.14445V13.8111C0 14.7278 0.75 15.4778 1.66667 15.4778H13.3333C14.25 15.4778 15 14.7278 15 13.8111V2.14445C15 1.22778 14.25 0.477783 13.3333 0.477783ZM3.33333 3.81112H5V5.47778H3.33333V3.81112ZM3.33333 7.14445H5V8.81112H3.33333V7.14445ZM3.33333 10.4778H5V12.1444H3.33333V10.4778ZM11.6667 12.1444H6.66667V10.4778H11.6667V12.1444ZM11.6667 8.81112H6.66667V7.14445H11.6667V8.81112ZM11.6667 5.47778H6.66667V3.81112H11.6667V5.47778Z"
      fill="white"
    />
  </svg>
);

const PrizeIcon = (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.44957 0.519197C5.58957 0.502197 5.74457 0.502197 5.92457 0.502197H12.5306C12.7106 0.502197 12.8656 0.502197 13.0056 0.519197C13.5025 0.581025 13.9647 0.80688 14.3188 1.16099C14.6729 1.51511 14.8987 1.97724 14.9606 2.4742L14.9636 2.5022H15.7696C15.9816 2.5002 16.4466 2.4962 16.8306 2.7532C17.3276 3.0842 17.4776 3.6532 17.4776 4.2522C17.4776 6.9782 16.0246 8.7982 14.1696 9.8092C13.1346 11.6932 11.2226 13.0022 9.22757 13.0022C7.70857 13.0022 6.26757 12.1802 5.23057 11.0432C4.886 10.6673 4.58354 10.2548 4.32857 9.8132C2.47457 8.8072 0.97757 6.9892 0.97757 4.2522C0.97757 3.6522 1.12757 3.0842 1.62357 2.7532C2.00857 2.4962 2.47357 2.5002 2.68557 2.5022H3.49157L3.49457 2.4742C3.5564 1.97724 3.78225 1.51511 4.13637 1.16099C4.49048 0.80688 4.95261 0.581025 5.44957 0.519197ZM15.9596 4.0072C15.8823 4.00254 15.8049 4.00087 15.7276 4.0022H14.9746L14.9426 7.2922C15.5806 6.5242 15.9776 5.5222 15.9776 4.2522C15.9776 4.1342 15.9686 4.0562 15.9596 4.0072ZM2.47757 4.2522C2.47757 5.4892 2.86557 6.4722 3.48757 7.2342C3.42157 6.4882 3.43957 5.7302 3.45757 4.9762C3.4669 4.64953 3.47257 4.32486 3.47457 4.0022H2.72757C2.61857 4.0022 2.54957 4.0022 2.49657 4.0072C2.48251 4.08808 2.47615 4.17011 2.47757 4.2522ZM9.45757 3.8072C9.43836 3.7621 9.40631 3.72365 9.36542 3.69662C9.32453 3.66959 9.27659 3.65518 9.22757 3.65518C9.17855 3.65518 9.13061 3.66959 9.08972 3.69662C9.04883 3.72365 9.01678 3.7621 8.99757 3.8072L8.49257 5.0192C8.47495 5.0615 8.44604 5.09814 8.40901 5.12512C8.37197 5.1521 8.32823 5.16839 8.28257 5.1722L6.97357 5.2772C6.92433 5.2811 6.87735 5.29949 6.83856 5.33007C6.79977 5.36064 6.7709 5.40202 6.7556 5.44899C6.7403 5.49595 6.73926 5.54639 6.7526 5.59395C6.76594 5.64151 6.79307 5.68404 6.83057 5.7162L7.82757 6.5702C7.86242 6.60018 7.88832 6.6392 7.90244 6.68295C7.91655 6.7267 7.91833 6.7735 7.90757 6.8182L7.60357 8.0942C7.59185 8.14235 7.5947 8.1929 7.61177 8.23943C7.62884 8.28596 7.65935 8.32636 7.69943 8.35551C7.73951 8.38466 7.78735 8.40124 7.83688 8.40314C7.8864 8.40504 7.93537 8.39219 7.97757 8.3662L9.09757 7.6822C9.13674 7.65835 9.18171 7.64574 9.22757 7.64574C9.27343 7.64574 9.3184 7.65835 9.35757 7.6822L10.4776 8.3662C10.5198 8.39219 10.5687 8.40504 10.6183 8.40314C10.6678 8.40124 10.7156 8.38466 10.7557 8.35551C10.7958 8.32636 10.8263 8.28596 10.8434 8.23943C10.8604 8.1929 10.8633 8.14235 10.8516 8.0942L10.5466 6.8182C10.5358 6.7735 10.5376 6.7267 10.5517 6.68295C10.5658 6.6392 10.5917 6.60018 10.6266 6.5702L11.6246 5.7162C11.6624 5.68412 11.6898 5.64151 11.7034 5.59379C11.7169 5.54608 11.716 5.4954 11.7007 5.44823C11.6853 5.40105 11.6563 5.3595 11.6173 5.32887C11.5783 5.29824 11.531 5.2799 11.4816 5.2762L10.1736 5.1722C10.1277 5.16857 10.0838 5.15236 10.0466 5.12537C10.0093 5.09838 9.98027 5.06164 9.96257 5.0192L9.45757 3.8072ZM9.22757 13.5022C9.42648 13.5022 9.61725 13.5812 9.7579 13.7219C9.89855 13.8625 9.97757 14.0533 9.97757 14.2522V16.5022H13.2276C13.4265 16.5022 13.6172 16.5812 13.7579 16.7219C13.8986 16.8625 13.9776 17.0533 13.9776 17.2522C13.9776 17.4511 13.8986 17.6419 13.7579 17.7825C13.6172 17.9232 13.4265 18.0022 13.2276 18.0022H5.22757C5.02866 18.0022 4.83789 17.9232 4.69724 17.7825C4.55659 17.6419 4.47757 17.4511 4.47757 17.2522C4.47757 17.0533 4.55659 16.8625 4.69724 16.7219C4.83789 16.5812 5.02866 16.5022 5.22757 16.5022H8.47757V14.2522C8.47757 14.0533 8.55659 13.8625 8.69724 13.7219C8.83789 13.5812 9.02866 13.5022 9.22757 13.5022Z"
      fill="white"
    />
  </svg>
);

const ArrowRight = (
  <svg
    width="50"
    height="22"
    viewBox="0 0 50 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_882_2072)">
      <path
        d="M39.5491 3.27246L44.4579 8.18119M44.4579 8.18119L39.5491 13.0899M44.4579 8.18119L6.29419 8.18119"
        stroke="white"
        stroke-width="3"
        stroke-linecap="round"
        stroke-linejoin="round"
        shape-rendering="crispEdges"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_882_2072"
        x="0.794189"
        y="0.772461"
        width="49.1637"
        height="20.8175"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="3" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.114278 0 0 0 0 0.274479 0 0 0 0 0.689115 0 0 0 1 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_882_2072"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_882_2072"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

const HomeIcon = (
  <svg
    width="16"
    height="15"
    viewBox="0 0 16 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 5.14178V1.18115H10.5V3.00771L8 0.681152L0 8.18115H2V14.6812H6.5V9.68115H9.5V14.6812H14V8.18115H16L13 5.14178Z"
      fill="white"
    />
  </svg>
);

const BonusIcon = (
  <svg
    width="15"
    height="15"
    viewBox="0 0 15 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.26827 0.681152C3.63937 0.681152 3.03623 0.930981 2.59154 1.37568C2.14684 1.82038 1.89701 2.42352 1.89701 3.05241V3.32369C1.89701 3.74862 2.02316 4.14414 2.24037 4.47517H1.42276C1.04542 4.47517 0.683535 4.62507 0.416716 4.89189C0.149897 5.1587 0 5.52059 0 5.89793V6.84643C0 7.22377 0.149897 7.58565 0.416716 7.85247C0.683535 8.11929 1.04542 8.26919 1.42276 8.26919H6.63953V4.47517H7.58804V8.26919H12.8048C13.1822 8.26919 13.544 8.11929 13.8109 7.85247C14.0777 7.58565 14.2276 7.22377 14.2276 6.84643V5.89793C14.2276 5.52059 14.0777 5.1587 13.8109 4.89189C13.544 4.62507 13.1822 4.47517 12.8048 4.47517H11.9872C12.2044 4.14414 12.3306 3.74862 12.3306 3.32369V3.05241C12.3306 2.42352 12.0807 1.82038 11.636 1.37568C11.1913 0.930981 10.5882 0.681152 9.9593 0.681152C8.75185 0.681152 7.69427 1.32614 7.11378 2.28982C6.81856 1.79898 6.40136 1.39288 5.90274 1.111C5.40412 0.829112 4.84105 0.681032 4.26827 0.681152ZM7.58804 4.47517H10.2306C10.8661 4.47517 11.3821 3.95918 11.3821 3.32369V3.05241C11.3821 2.67508 11.2322 2.31319 10.9653 2.04637C10.6985 1.77955 10.3366 1.62966 9.9593 1.62966C9.3304 1.62966 8.72726 1.87949 8.28256 2.32418C7.83787 2.76888 7.58804 3.37202 7.58804 4.00092V4.47517ZM6.63953 4.47517H3.997C3.3615 4.47517 2.84551 3.95918 2.84551 3.32369V3.05241C2.84551 2.67508 2.99541 2.31319 3.26223 2.04637C3.52905 1.77955 3.89093 1.62966 4.26827 1.62966C4.57967 1.62966 4.88802 1.69099 5.17571 1.81016C5.46341 1.92933 5.72481 2.10399 5.94501 2.32418C6.1652 2.54438 6.33986 2.80578 6.45903 3.09348C6.5782 3.38117 6.63953 3.68952 6.63953 4.00092V4.47517Z"
      fill="white"
    />
    <path
      d="M6.63953 9.2177H0.948501V12.5375C0.948501 13.1664 1.19833 13.7695 1.64303 14.2142C2.08773 14.6589 2.69086 14.9087 3.31976 14.9087H6.63953V9.2177ZM7.58803 14.9087H10.9078C11.5367 14.9087 12.1398 14.6589 12.5845 14.2142C13.0292 13.7695 13.2791 13.1664 13.2791 12.5375V9.2177H7.58803V14.9087Z"
      fill="white"
    />
  </svg>
);

const RankIcon = (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.808 16.1812C0.579333 16.1812 0.387666 16.1038 0.233 15.9492C0.0783331 15.7945 0.000666667 15.6025 0 15.3732V6.99716C0 6.75916 0.0776665 6.56382 0.233 6.41116C0.388333 6.25849 0.58 6.18182 0.808 6.18116H3.347C3.57567 6.18116 3.76733 6.25849 3.922 6.41316C4.07667 6.56849 4.154 6.76049 4.154 6.98916V15.3662C4.154 15.6035 4.07667 15.7985 3.922 15.9512C3.76733 16.1045 3.57567 16.1812 3.347 16.1812H0.808ZM6.776 16.1812C6.54 16.1812 6.34533 16.1038 6.192 15.9492C6.03867 15.7938 5.962 15.6018 5.962 15.3732V0.989157C5.962 0.76049 6.03933 0.56849 6.194 0.413156C6.34867 0.257823 6.54067 0.18049 6.77 0.181157H9.687C9.923 0.181157 10.1177 0.25849 10.271 0.413156C10.4243 0.56849 10.501 0.76049 10.501 0.989157V15.3732C10.501 15.6025 10.4233 15.7945 10.268 15.9492C10.1127 16.1038 9.921 16.1812 9.693 16.1812H6.776ZM13.116 16.1812C12.8867 16.1812 12.6947 16.1045 12.54 15.9512C12.3853 15.7978 12.308 15.6078 12.308 15.3812V8.98116C12.308 8.75449 12.3853 8.56449 12.54 8.41116C12.6947 8.25782 12.8867 8.18116 13.116 8.18116H15.654C15.8827 8.18116 16.0747 8.25782 16.23 8.41116C16.3853 8.56449 16.4627 8.75449 16.462 8.98116V15.3812C16.462 15.6078 16.3847 15.7978 16.23 15.9512C16.0753 16.1045 15.8833 16.1812 15.654 16.1812H13.116Z"
      fill="white"
    />
  </svg>
);

export default function HowToParticipate({gotoHome}: Props) {
  const {country} = useParams()

  return (
    <div className="w-full h-full flex flex-col items-center justify-center gap-4 pb-16">
      <Title text={translate('howToParticipate_Title', country as Language)} />

      <div className="grid grid-cols-2 gap-2">
        <div className="flex flex-col gap-2 items-end">
          <Card
            content={
              <div className="flex flex-col gap-2 !p-2 max-w-[140px]">
                <div className="flex gap-2 items-center">
                  {HomeIcon}
                  <h3 className="text-[14px] font-bold">
                    {translate('howToParticipate_Home', country as Language)}
                  </h3>
                </div>
                <p className="text-[10px]">
                  {translate('howToParticipate_HomeDesc', country as Language)}
                </p>
              </div>
            }
          />
          {arrowBottonLeft}
          <Card
            content={
              <div className="flex flex-col gap-2 !p-2 max-w-[140px]">
                <div className="flex gap-2 items-center">
                  {BonusIcon}

                  <h3 className="text-[14px] font-bold">
                    {translate('howToParticipate_Bonus', country as Language)}
                  </h3>
                </div>
                <p className="text-[10px]">
                  {translate('howToParticipate_BonusDesc', country as Language)}
                </p>
              </div>
            }
          />
          {arrowBottonLeft}
          <Card
            content={
              <div className="flex flex-col gap-2 !p-2 max-w-[140px]">
                <div className="flex gap-2 items-center">
                  {RankIcon}

                  <h3 className="text-[14px] font-bold">
                    {translate('howToParticipate_Rank', country as Language)}
                  </h3>
                </div>
                <p className="text-[10px]">
                  {translate('howToParticipate_RankDesc', country as Language)}
                </p>
              </div>
            }
          />
        </div>
        <div className="flex flex-col gap-2 items-start mt-6">
          {arrowBottonRight}
          <Card
            content={
              <div className="flex flex-col gap-2 !p-2 max-w-[140px]">
                <div className="flex gap-2 items-center">
                  {PlayIcon}
                  <h3 className="text-[14px] font-bold">
                    {translate('howToParticipate_Play', country as Language)}
                  </h3>
                </div>
                <p className="text-[10px]">
                  {translate('howToParticipate_PlayDesc', country as Language)}
                </p>
              </div>
            }
          />
          {arrowBottonRight}
          <Card
            content={
              <div className="flex flex-col gap-2 !p-2 max-w-[140px]">
                <div className="flex gap-2 items-center">
                  {SubmitIcon}
                  <h3 className="text-[14px] font-bold">
                    {translate('howToParticipate_Submit', country as Language)}
                  </h3>
                </div>
                <p className="text-[10px]">
                  {translate(
                    'howToParticipate_SubmitDesc',
                    country as Language
                  )}
                </p>
              </div>
            }
          />
          <div className="flex items-center gap-2 mt-2">
            {ArrowRight}
            <div
              style={{
                backdropFilter: 'blur(0.5px)',
                backgroundColor: 'rgba(0, 23, 84, 0.6)',
              }}
              className="rounded-full size-[75px] border-2 border-white flex flex-col items-center justify-center gap-1"
            >
              {PrizeIcon}
              <p className="text-[10px]">
                {translate('howToParticipate_Prize', country as Language)}
              </p>
            </div>
          </div>
        </div>
      </div>

      <Button
        className="!w-[300px]"
        label={translate('howToParticipate_GotoHomePage', country as Language)}
        onClick={() => gotoHome()}
      />
    </div>
  );
}
