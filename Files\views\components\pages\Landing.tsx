declare let React: any

import getPrizeByRegion, {PrizeType} from '../../config/getPrizeByRegion'
import {Language, translate} from '../../config/translation'

import Button from '../atoms/Button'
import DropdownRegion from '../atoms/DropdownRegion'
import Title from '../atoms/Title'
import Header from '../organisms/Header'
import {UserData} from '../../types/UserData'
import {
  NewPlayer,
  ReturnPlayer,
} from '../../analytics/events'
import Footer from '../organisms/Footer'

declare let ReactRouterDOM: any
const {useParams} = ReactRouterDOM

interface Props {
  gameStatus: any;
  setGameStatus: (status: any) => void;
  user: UserData | null;
  setUser: (user: UserData) => void;
  firstTimePlaying: () => void;
  gotoPage: (page: any) => void;
  gotoHome: () => void;
  returnToPlay: () => void;
  signOut: () => void;
}
const Landing = ({
  gameStatus,
  setGameStatus,
  user,
  gotoHome,
  gotoPage,
  firstTimePlaying,
  returnToPlay,
  signOut,
}: Props) => {
  const {country} = useParams()
  const alltimePrize = getPrizeByRegion(country, 'landing') as PrizeType
  if (country === 'th') {
    return (
      <div
        className={`absolute top-0 z-30 w-full h-full flex flex-col gap-2 items-center   justify-between
         pt-20`}
      >
        <Header
          className='!absolute !top-0'
          goToPage={gotoPage}
          gameStatus={gameStatus}
          signOut={signOut}
        />
        <div className='flex flex-col gap-4 items-center justify-center'>
          <Title
            text={translate('prize_title', country as Language)}
            className=''
          />
          <DropdownRegion signOut={signOut} />

          <>
            <div className='flex flex-col gap-1 items-center justify-center'>
              <p className='text-xs text-center mt-6'>
                {translate('prize_desc', country as Language)}
              </p>
              <img
                src={alltimePrize.prize}
                alt='OREO MILKWAY DUNK'
                className='fade-in max-w-[250px]'
              />
              <p className='text-xs text-center max-w-[300px]'>
                {alltimePrize.description}
              </p>
            </div>
          </>
          <svg
            width='18'
            height='30'
            viewBox='0 0 18 30'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M16.3 21.5209L9.00004 28.8393L1.70003 21.5209M9.00004 27.8228L9.00004 0.985509'
              stroke='white'
              stroke-width='1.5'
              stroke-miterlimit='10'
              stroke-linecap='square'
            />
          </svg>
          <Title
            text={translate('gameTitle', country as Language)}
            className='mt-6 fade-in uppercase'
          />
        </div>
        <div
          className={' mt-6 mb-20 gap-7 flex flex-col items-center justify-center'}
        >
          <div
            className={'w-full grid grid-cols-2   gap-4 px-4 max-w-[400px] self-center '}
          >
            <Button
              onClick={() => setGameStatus('bonus-level')}
              label={translate(
                'scanCookieToGetExtra6000Points',
                country as Language
              )}
              variant={user?.allow_bonus_level ? 'blue' : 'translucent'}
              disabled={!user?.allow_bonus_level}
              className='text-[17px] col-span-2'
            />
            <Button
              onClick={() => {
                returnToPlay()
                ReturnPlayer()
              }}
                 id='ReturnPlayer'
              label={translate('returnToPlay', country as Language)}
               className='text-base '
            />
            <Button
              onClick={() => {
                firstTimePlaying()
                NewPlayer()
              }}
                 id='NewPlayer'
              label={translate('firstTimePlaying', country as Language)}
               className='text-base '
            />

            <Button
              onClick={() => {
                gotoPage('the-prizes')
              }}
              label={translate('thePrizes', country as Language)}
              className='text-base col-span-2'
            />
          </div>
        </div>
        <Footer className='!-bottom-10' goToPage={gotoPage} />
      </div>
    )
  }
  if (country === 'id') {
    return (
      <div
        className={`absolute top-0 z-30 w-full h-full flex flex-col gap-2 items-center   justify-between
         pt-20`}
      >
        <Header
          className='!absolute !top-0'
          goToPage={gotoPage}
          gameStatus={gameStatus}
          signOut={signOut}
        />
        <div className='flex flex-col gap-4 items-center justify-center'>
          <Title
            text={translate('prize_title', country as Language)}
            className=''
          />
          <DropdownRegion signOut={signOut} />

          <>
            <div className='flex flex-col gap-1 items-center justify-center'>
              <p className='text-xs text-center mt-6'>
                {translate('prize_desc', country as Language)}
              </p>
              <img
                src={alltimePrize.prize}
                alt='OREO MILKWAY DUNK'
                className='fade-in max-w-[300px]'
              />
              <p className='text-xs text-center max-w-[300px]'>
                {alltimePrize.description}
              </p>
            </div>
          </>

          <Title

            text={translate('gameTitle', country as Language)}
            className='mt-6 fade-in uppercase'
          />
        </div>
        <div
          className={' mt-6 mb-20 gap-7 flex flex-col items-center justify-center'}
        >
          <svg
            width='18'
            height='30'
            viewBox='0 0 18 30'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M16.3 21.5209L9.00004 28.8393L1.70003 21.5209M9.00004 27.8228L9.00004 0.985509'
              stroke='white'
              stroke-width='1.5'
              stroke-miterlimit='10'
              stroke-linecap='square'
            />
          </svg>

          <div
            className={'w-full grid grid-cols-2   gap-4 px-4 max-w-[400px] self-center '}
          >
            <Button
              onClick={() => setGameStatus('bonus-level')}
              label={translate(
                'scanCookieToGetExtra6000Points',
                country as Language
              )}
              variant={user?.allow_bonus_level ? 'blue' : 'translucent'}
              disabled={!user?.allow_bonus_level}
              className='col-span-2'
            />
            <Button
              onClick={() => {
                returnToPlay()
                ReturnPlayer()
              }}
                 id='ReturnPlayer'
              label={translate('returnToPlay', country as Language)}
            />
            <Button
              onClick={() => {
                firstTimePlaying()
                NewPlayer()
              }}
                 id='NewPlayer'
              label={translate('firstTimePlaying', country as Language)}
              className=''
            />

            <Button
              onClick={() => {
                gotoPage('the-prizes')
              }}
              label={translate('thePrizes', country as Language)}
              className='col-span-2'
            />
          </div>
        </div>
        <Footer className='!-bottom-10' goToPage={gotoPage} />
      </div>
    )
  }
  return (
    <div
      className={`absolute top-0 z-30 w-full h-full flex flex-col gap-2 items-center ${
        country !== 'id' ? 'justify-between' : 'justify-between'
      } pt-20`}
    >
      <Header
        className='!absolute !top-0'
        goToPage={gotoPage}
        gameStatus={gameStatus}
        signOut={signOut}
      />
      <div className='flex flex-col gap-4 items-center justify-center'>
        <Title
          text={translate('prize_title', country as Language)}
          className=''
        />
        <DropdownRegion signOut={signOut}/>

        {country === 'id' && (
          <>
            <div className='flex flex-col gap-1 items-center justify-center'>
              <p className='text-xs text-center mt-6'>
                {translate('prize_desc', country as Language)}
              </p>
              <img
                src={alltimePrize.prize}
                alt='OREO MILKWAY DUNK'
                className='fade-in max-w-[300px]'
              />
              <p className='text-xs text-center max-w-[300px]'>
                {alltimePrize.description}
              </p>
            </div>
          </>
        )}

        <Title

          text={translate('gameTitle', country as Language)}
          className='mt-6 fade-in uppercase'
        />
      </div>
      <div
        className={`${
          country !== 'id' ? 'mt-32 mb-20' : 'mt-6 mb-20 gap-7'
        } flex flex-col items-center justify-center`}
      >
        {country === 'id' && (
          <svg
            width='18'
            height='30'
            viewBox='0 0 18 30'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M16.3 21.5209L9.00004 28.8393L1.70003 21.5209M9.00004 27.8228L9.00004 0.985509'
              stroke='white'
              stroke-width='1.5'
              stroke-miterlimit='10'
              stroke-linecap='square'
            />
          </svg>
        )}
        <div
          className={'w-full grid grid-cols-2   gap-4 px-4 max-w-[400px] self-center '}
        >
          <Button
            onClick={() => setGameStatus('bonus-level')}
            label={translate(
              'scanCookieToGetExtra6000Points',
              country as Language
            )}
            variant={user?.allow_bonus_level ? 'blue' : 'translucent'}
            disabled={!user?.allow_bonus_level}
            className='col-span-2'
          />
          <Button
            onClick={() => {
              returnToPlay()
              ReturnPlayer()
            }}
               id='ReturnPlayer'
            label={translate('returnToPlay', country as Language)}
          />
          <Button
            onClick={() => {
              firstTimePlaying()
              NewPlayer()
            }}
               id='NewPlayer'
            label={translate('firstTimePlaying', country as Language)}
            className=''
          />

          <Button
            onClick={() => {
              gotoPage('the-prizes')
            }}
            label={translate('thePrizes', country as Language)}
            className='col-span-2'
          />
        </div>
        {country !== 'id' && (
          <div className='flex flex-col   pt-7 items-center justify-center'>
            <svg
              width='18'
              height='30'
              viewBox='0 0 18 30'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M16.3 21.5209L9.00004 28.8393L1.70003 21.5209M9.00004 27.8228L9.00004 0.985509'
                stroke='white'
                stroke-width='1.5'
                stroke-miterlimit='10'
                stroke-linecap='square'
              />
            </svg>

            <div className='flex flex-col gap-1 items-center justify-center'>
              <img
                src={alltimePrize.prize}
                alt='OREO MILKWAY DUNK'
                className='mt-6 fade-in max-w-[300px]'
              />
              <p className='text-xs text-center max-w-[300px]'>
                {alltimePrize.description}
              </p>
            </div>
          </div>
        )}
      </div>
      <Footer className='!-bottom-10' goToPage={gotoPage} />
    </div>
  )
}

export default Landing
